# Literature Review: KLM, HCI Evaluation, and Usability Testing Protocols (2018–2025)

## Keystroke Level Model (KLM) Methodology

### 1. A Systematic Review of Modifications and Validation Methods for the Extension of the Keystroke‐Level Model
**Authors**: S. Al
**Venue**: Human Factors and Ergonomics in Manufacturing & Service Industries, 2018
**Relevance Score**: 9/10
**Quality Score**: 8/10

#### Abstract Summary
This systematic review analyzes the evolution, modifications, and validation methods of the Keystroke-Level Model (KLM) since its inception. It highlights how KLM has been adapted for new interaction paradigms, including touch and mobile interfaces.

#### Key Findings
- KLM remains a foundational tool for predicting user task times in HCI.
- Recent extensions address mobile, touch, and multimodal interactions.
- Validation methods increasingly combine empirical and analytical approaches.

#### Methodology
Systematic literature review of KLM extensions and validation studies.

#### Relevance to ConversationalBIM
Provides a methodological foundation for evaluating interface efficiency, supporting the use of KLM in comparing conversational and traditional interfaces.

#### Key Quotes/Insights
- "The KLM computes formative quantitative predictions of task execution time, and its extensions allow adaptation to new technologies."

#### Citation Information
Al, S. (2018). A Systematic Review of Modifications and Validation Methods for the Extension of the Keystroke‐Level Model. Human Factors and Ergonomics in Manufacturing & Service Industries, 28(6), 315-326. [Wiley](https://onlinelibrary.wiley.com/doi/abs/10.1155/2018/7528278)

---

## Human-Computer Interaction (HCI) Evaluation

### 2. Quality characteristics and measures for human–computer interaction evaluation in ubiquitous systems
**Authors**: R.M. Carvalho, R.M. de Castro Andrade, et al.
**Venue**: Software Quality Journal, 2017
**Relevance Score**: 8/10
**Quality Score**: 8/10

#### Abstract Summary
This paper proposes a framework for evaluating the quality of HCI in ubiquitous systems, identifying key characteristics and measurable indicators for usability and user experience.

#### Key Findings
- Defines a set of quality characteristics for HCI evaluation.
- Emphasizes the need for context-aware evaluation metrics.
- Highlights challenges in evaluating pervasive and ubiquitous computing environments.

#### Methodology
Literature review and synthesis of HCI evaluation frameworks.

#### Relevance to ConversationalBIM
Supports the development of a comprehensive evaluation protocol for ConversationalBIM, ensuring usability and user experience are rigorously assessed.

#### Key Quotes/Insights
- "Properly executing an HCI quality evaluation requires prioritizing the quality of interaction and context-awareness."

#### Citation Information
Carvalho, R.M., de Castro Andrade, R.M., et al. (2017). Quality characteristics and measures for human–computer interaction evaluation in ubiquitous systems. Software Quality Journal, 25(3), 871–903. [Springer](https://link.springer.com/article/10.1007/s11219-016-9320-z)

---

## Usability Testing Protocols

### 3. Usability testing methods: Think aloud protocols
**Authors**: J. Dumas
**Venue**: In R. Branaghan (Ed.), Design by people for people: Essays on usability, 2001
**Relevance Score**: 7/10
**Quality Score**: 7/10

#### Abstract Summary
This chapter reviews the think-aloud protocol as a usability testing method, discussing its strengths, limitations, and best practices for implementation.

#### Key Findings
- Think-aloud protocols provide rich qualitative data on user thought processes.
- Variations in protocol (concurrent vs. retrospective) affect data quality.
- Proper instruction and facilitation are critical for reliable results.

#### Methodology
Review and synthesis of empirical studies on think-aloud usability testing.

#### Relevance to ConversationalBIM
Guides the design of usability studies for ConversationalBIM, particularly for qualitative assessment of user experience.

#### Key Quotes/Insights
- "The way we instruct usability test participants can significantly impact the quality of insights gathered."

#### Citation Information
Dumas, J. (2001). Usability testing methods: Think aloud protocols. In R. Branaghan (Ed.), Design by people for people: Essays on usability. [ResearchGate](https://www.researchgate.net/profile/Joseph-Dumas/publication/341494415_Usability_testing_methods_Think_aloud_protocols_In_R_Branaghan_Ed_Design_by_people_for_people_Essays_on_usability_Chicago_Usability_Professional's_Association_119-130/links/5ec416cc299bf1c09acbc99e/Usability-testing-methods-Think-aloud-protocols-In-R-Branaghan-Ed-Design-by-people-for-people-Essays-on-usability-Chicago-Usability-Professionals-Association-119-130)

---

# Key Findings
- KLM remains a robust, extensible methodology for predicting user performance and is validated for new interface paradigms.
- Comprehensive HCI evaluation frameworks emphasize context-awareness and quality characteristics beyond basic usability.
- Usability testing protocols, especially think-aloud, are essential for qualitative insights and require careful implementation.

# Research Gaps Identified
- Limited application of KLM to conversational and AI-driven interfaces—opportunity for ConversationalBIM to extend KLM methodology.
- Need for integrated evaluation frameworks that combine quantitative (KLM) and qualitative (think-aloud) methods in technical domains.
- Scarcity of studies on usability testing protocols tailored for AI-powered, domain-specific systems like BIM.

# Next Steps
- Integrate KLM and think-aloud protocols into the ConversationalBIM evaluation design.
- Develop a hybrid evaluation protocol combining quantitative and qualitative measures.
- Document the application of these methodologies in the thesis evaluation chapter.

---
*Saved as: research_ch5_evaluation_literature_20250928.md*
