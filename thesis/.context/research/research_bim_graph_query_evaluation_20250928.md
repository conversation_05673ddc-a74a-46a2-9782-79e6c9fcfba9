# Evaluation Protocols and Usability Study Designs for BIM Systems and Graph Query Interfaces

## 1. Introduction

Evaluating the usability and effectiveness of Building Information Modeling (BIM) systems and graph query interfaces is critical for advancing user-centered design in the construction and information management domains. With the emergence of conversational AI as a novel interface paradigm, rigorous evaluation protocols are needed to compare traditional interfaces (e.g., SPARQL editors, graph UIs) with these innovative approaches. This review synthesizes the state of the art in experimental setups, metrics, and challenges for such comparative studies.

## 2. Experimental Setups in BIM and Graph Query Evaluation

### 2.1. BIM System Usability Studies
- **Controlled Laboratory Experiments:** Most BIM usability studies employ controlled lab settings where participants perform predefined tasks using BIM tools or interfaces ([Baudoux & Leclercq, 2022](https://link.springer.com/chapter/10.1007/978-3-031-16538-2_31)).
- **Immersive and Mixed Reality Protocols:** Recent protocols incorporate VR/AR environments to assess BIM usability in more realistic or collaborative scenarios ([<PERSON><PERSON><PERSON><PERSON> et al., 2020](https://iris.unibs.it/handle/11379/528172); [<PERSON><PERSON><PERSON> et al., 2021](https://ec-3.org/publications/conference/paper/?id=EC32021_174)).
- **Systematic Review Protocols:** Some studies focus on reviewing and synthesizing evaluation methods for BIM-based AR tools ([Sidani et al., 2019](https://journals.fe.up.pt/index.php/ijooes/article/view/2184-0954_003.002_0006)).

### 2.2. Graph Query Interface Evaluation
- **Task-Based User Studies:** Users are asked to complete information retrieval or query construction tasks using different interfaces (SPARQL editors, visual query builders, conversational agents) ([Kuric et al., 2019](https://link.springer.com/chapter/10.1007/978-3-030-33220-4_24)).
- **Between-Subjects and Within-Subjects Designs:** Both experimental designs are used, with within-subjects preferred for direct comparison of interfaces.
- **Empirical Evaluation of Graph Visualizations:** Studies focus on graph interpretation, memorability, and creation, often using standardized tasks ([Burch et al., 2020](https://ieeexplore.ieee.org/abstract/document/9309216/)).

## 3. Metrics for Usability and Performance

### 3.1. Quantitative Metrics
- **Task Completion Time:** Measures efficiency; often used in KLM-based evaluations ([Card et al., 1980](https://dl.acm.org/doi/pdf/10.1145/358886.358895)).
- **Error Rate:** Frequency and severity of user errors during task execution.
- **Success Rate:** Proportion of tasks completed correctly.
- **Keystroke-Level Model (KLM):** Predicts execution time for interface operations, enabling objective comparison of interaction efficiency ([Kieras, 2001](https://www.cs.loyola.edu/~lawrie/CS774/S06/homework/klm.pdf)).
- **Number of Steps/Interactions:** Counts clicks, keystrokes, or conversational turns.

### 3.2. Qualitative Metrics
- **System Usability Scale (SUS):** Standardized questionnaire for subjective usability assessment.
- **NASA-TLX:** Measures perceived workload.
- **Think-Aloud Protocols:** Capture user reasoning and interface challenges ([Dumas, 2001](https://www.researchgate.net/profile/Joseph-Dumas/publication/341494415_Usability_testing_methods_Think_aloud_protocols_In_R_Branaghan_Ed_Design_by_people_for_people_Essays_on_usability_Chicago_Usability_Professional's_Association_119-130/links/5ec416cc299bf1c09acbc99e/Usability-testing-methods-Think-aloud-protocols-In-R-Branaghan-Ed-Design-by-people-for-people-Essays-on-usability-Chicago-Usability-Professionals-Association-119-130)).
- **User Satisfaction and Preference:** Post-study interviews or questionnaires.

## 4. Challenges in Comparative Evaluation

- **Task Equivalence:** Ensuring tasks are comparable across different interface paradigms (e.g., SPARQL editor vs. conversational agent).
- **Participant Expertise:** Balancing domain knowledge and technical skill among participants.
- **Learning Effects:** Mitigating bias from repeated exposure in within-subjects designs.
- **Ecological Validity:** Simulating real-world complexity while maintaining experimental control.
- **Measuring Cognitive Load:** Capturing mental effort, especially in complex query construction.
- **Subjectivity in Qualitative Data:** Interpreting think-aloud and interview data reliably.

## 5. Notable Protocols and Methodological Innovations

- **Mixed-Method Approaches:** Combining quantitative (KLM, task time) and qualitative (think-aloud, SUS) data for comprehensive evaluation ([Kuric et al., 2019](https://link.springer.com/chapter/10.1007/978-3-030-33220-4_24)).
- **Immersive/AR Usability Protocols:** Protocols for evaluating BIM in VR/AR settings emphasize realism and collaborative workflows ([Mastrolembo Ventura et al., 2020](https://iris.unibs.it/handle/11379/528172)).
- **Usability of Query Builders for Laypeople:** Focus on accessibility and learnability for non-expert users ([Kuric et al., 2019](https://link.springer.com/chapter/10.1007/978-3-030-33220-4_24)).

## 6. Research Gaps and Opportunities for ConversationalBIM

- **Lack of Standardized Protocols for Conversational Interfaces:** Few studies rigorously compare conversational AI with traditional BIM or graph query interfaces using standardized tasks and metrics.
- **Limited KLM Application Beyond Traditional GUIs:** KLM and similar models are rarely applied to conversational or multi-modal interfaces.
- **Ecological Validity in Real-World BIM Workflows:** Most studies use simplified tasks; there is a need for evaluations embedded in authentic, complex BIM scenarios.
- **Cognitive Load and User Experience:** More research is needed on measuring and optimizing cognitive load in conversational and hybrid interfaces.

## 7. References and Further Reading

- Card, S.K., Moran, T.P., & Newell, A. (1980). The keystroke-level model for user performance time with interactive systems. [ACM DL](https://dl.acm.org/doi/pdf/10.1145/358886.358895)
- Kieras, D. (2001). Using the keystroke-level model to estimate execution times. [PDF](https://www.cs.loyola.edu/~lawrie/CS774/S06/homework/klm.pdf)
- Kuric, E., Fernández, J.D., & Drozd, O. (2019). Knowledge graph exploration: a usability evaluation of query builders for laypeople. [Springer](https://link.springer.com/chapter/10.1007/978-3-030-33220-4_24)
- Burch, M., Huang, W., Wakefield, M., & Purchase, H.C. (2020). The state of the art in empirical user evaluation of graph visualizations. [IEEE Xplore](https://ieeexplore.ieee.org/abstract/document/9309216/)
- Baudoux, G., & Leclercq, P. (2022). Usability of BIM in preliminary design: a study of the relevance of the different representations allowed by BIM. [Springer](https://link.springer.com/chapter/10.1007/978-3-031-16538-2_31)
- Mastrolembo Ventura, S., Castronovo, F., et al. (2020). A design review session protocol for the implementation of immersive virtual reality in usability-focused analysis. [IRIS](https://iris.unibs.it/handle/11379/528172)
- Yilmaz, N., Ergen, E., & Artan, D. (2021). A usability test protocol for evaluating mixed reality environments. [EC-3](https://ec-3.org/publications/conference/paper/?id=EC32021_174)
- Sidani, A., Dinis, F., Sanhudo, L., & Duarte, J. (2019). Impact of BIM-based augmented reality interfaces on construction projects: protocol for a systematic review. [IJOOES](https://journals.fe.up.pt/index.php/ijooes/article/view/2184-0954_003.002_0006)
- Dumas, J. (2001). Usability testing methods: Think aloud protocols. [ResearchGate](https://www.researchgate.net/profile/Joseph-Dumas/publication/341494415_Usability_testing_methods_Think_aloud_protocols_In_R_Branaghan_Ed_Design_by_people_for_people_Essays_on_usability_Chicago_Usability_Professional's_Association_119-130/links/5ec416cc299bf1c09acbc99e/Usability-testing-methods-Think-aloud-protocols-In-R-Branaghan-Ed-Design-by-people-for-people-Essays-on-usability-Chicago-Usability-Professionals-Association-119-130)

---
*Prepared: 2025-09-28*

---

## Appendix: Additional Relevant Papers
- [The state of the art in empirical user evaluation of graph visualizations](https://ieeexplore.ieee.org/abstract/document/9309216/)
- [Knowledge graph exploration: a usability evaluation of query builders for laypeople](https://link.springer.com/chapter/10.1007/978-3-030-33220-4_24)
- [Usability of BIM in preliminary design](https://link.springer.com/chapter/10.1007/978-3-031-16538-2_31)
- [A design review session protocol for the implementation of immersive virtual reality in usability-focused analysis](https://iris.unibs.it/handle/11379/528172)
- [A usability test protocol for evaluating mixed reality environments](https://ec-3.org/publications/conference/paper/?id=EC32021_174)
- [Impact of BIM-based augmented reality interfaces on construction projects: protocol for a systematic review](https://journals.fe.up.pt/index.php/ijooes/article/view/2184-0954_003.002_0006)
