# Technical Documentation and Best Practices for Evaluation Methods

## 1. Keystroke Level Model (KLM) Methodology

### Key Points
- **Definition**: The Keystroke-Level Model (KLM) is a predictive model in Human-Computer Interaction (HCI) used to estimate the time it takes an expert user to perform routine, error-free tasks by breaking them down into low-level actions (keystrokes, mouse movements, mental preparations, etc.).
- **Operators**: KLM uses a set of basic operators: Keystroke (K), Pointing (P), Homing (H), Drawing (D), Mental preparation (M), and System Response (R).
- **Application**: KLM is best suited for evaluating the efficiency of user interfaces for expert users performing routine tasks. It is widely used for quantitatively comparing design alternatives prior to implementation.
- **Best Practices**:
  - Use KLM for routine, error-free tasks performed by experienced users.
  - Clearly define each operator and ensure consistency in their application.
  - Combine with other GOMS models (Goals, Operators, Methods, Selection rules) for more complex tasks.
  - Document all assumptions and task breakdowns for reproducibility.

### Official Standards / Guides
- **Usability Body of Knowledge (UXPA)**: KLM-GOMS overview and best practices  
  https://www.usabilitybok.org/klm-goms
- **Wikipedia - Keystroke-Level Model**: Detailed explanation of operators and methodology  
  https://en.wikipedia.org/wiki/Keystroke-level_model
- **Key Lime Interactive**: Practical guide to KLM analysis  
  https://info.keylimeinteractive.com/what-is-the-keystroke-level-model
- **Academic Reference (PDFs)**:
  - "Keystroke Level Model" (Penn State IST331 course material)  
    https://acs.ist.psu.edu/ist331/KLM.pdf
  - "Using the Keystroke-Level Model to Estimate Execution Times"  
    https://www.cs.umd.edu/~golbeck/INST631/KSM.pdf

---

## 2. Human-Computer Interaction (HCI) Evaluation

### Key Points
- **Definition**: HCI evaluation encompasses methods and standards for assessing the usability, effectiveness, and user experience of interactive systems.
- **Common Methods**: Heuristic evaluation, cognitive walkthroughs, usability testing, surveys, and predictive modeling (e.g., KLM).
- **Best Practices**:
  - Follow established usability heuristics (e.g., Nielsen’s Ten Heuristics, Shneiderman’s Eight Golden Rules).
  - Use both qualitative and quantitative evaluation methods.
  - Consider context of use, user characteristics, and task complexity.
  - Document evaluation procedures and findings systematically.

### Official Standards / Guides
- **International Standards**:
  - ISO 9241-11:2018 – Ergonomics of human-system interaction: Usability definitions and concepts  
    https://www.iso.org/standard/63500.html
  - ISO 9241-210:2019 – Human-centred design for interactive systems  
    https://www.iso.org/standard/77520.html
- **ScienceDirect**: Overview of international HCI and usability standards  
  https://www.sciencedirect.com/science/article/pii/S1071581901904835
- **Usability First**: HCI design approaches and evaluation methods  
  https://www.usabilityfirst.com/usability-methods/hci-design-approaches/index.html
- **Interaction Design Foundation (IxDF)**: HCI fundamentals and evaluation  
  https://www.interaction-design.org/literature/topics/human-computer-interaction

---

## 3. Usability Testing Protocols

### Key Points
- **Definition**: Usability testing is a systematic process where representative users perform tasks with a system while observers identify usability problems and measure user performance and satisfaction.
- **Types**: Moderated/unmoderated, remote/in-person, formative/summative.
- **Best Practices**:
  - Define clear objectives and success metrics for each test.
  - Use realistic scenarios and representative tasks.
  - Recruit participants that match the target user profile.
  - Limit the number of tasks per session (typically 3-5).
  - Pilot test the protocol before full deployment.
  - Record sessions and collect both quantitative (task completion time, error rates) and qualitative (user feedback) data.
  - Analyze results systematically and iterate on design.

### Official Standards / Guides
- **NIST Usability Standards**:  
  https://www.nist.gov/programs-projects/usability-standards
- **ISO 9241-210:2019**: Human-centred design for interactive systems  
  https://www.iso.org/standard/77520.html
- **Nielsen Norman Group (NNG)**: Usability Testing 101  
  https://www.nngroup.com/articles/usability-testing-101/
- **Interaction Design Foundation (IxDF)**: Usability Testing  
  https://www.interaction-design.org/literature/topics/usability-testing
- **User Interviews**: Usability testing best practices  
  https://www.userinterviews.com/blog/usability-testing-best-practices
- **UX Design Institute**: Step-by-step guide to usability testing  
  https://www.uxdesigninstitute.com/blog/guide-to-usability-testing-for-ux/
