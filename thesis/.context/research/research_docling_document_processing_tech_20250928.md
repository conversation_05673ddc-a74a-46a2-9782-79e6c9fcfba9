# Docling for Document Processing in Python: Official Documentation, Usage Guides, and Best Practices

## Overview
Docling is an open-source Python toolkit developed by IBM Research for advanced document processing and conversion. It is designed to parse a wide variety of document formats—including PDFs, DOCX, PPTX, XLSX, HTML, images, and audio files—into unified, richly structured representations suitable for downstream AI workflows, such as retrieval-augmented generation (RAG), LLM fine-tuning, and knowledge extraction. Docling is MIT-licensed, runs efficiently on commodity hardware, and is extensible for custom pipelines and models.

- **Official Documentation:** [Docling GitHub Pages](https://docling-project.github.io/docling/)
- **GitHub Repository:** [github.com/docling-project/docling](https://github.com/docling-project/docling)
- **Technical Report:** [arXiv:2408.09869](https://arxiv.org/abs/2408.09869)
- **PyPI:** [pypi.org/project/docling](https://pypi.org/project/docling/)

## Key Features
- **Multi-format Parsing:** Supports PDF, DOCX, PPTX, XLSX, HTML, images (PNG, TIFF, JPEG), audio (WAV, MP3, VTT), and more.
- **Advanced PDF Understanding:** Includes page layout, reading order, table structure (via TableFormer), code, formulas, and image classification.
- **Unified Document Model:** Uses the expressive `DoclingDocument` format for consistent downstream processing.
- **Export Options:** Outputs to Markdown, HTML, JSON, and DocTags.
- **Local Execution:** Can run fully offline for sensitive or air-gapped environments.
- **Plug-and-play Integrations:** Native support for LangChain, LlamaIndex, Crew AI, Haystack, and more.
- **Extensive OCR Support:** For scanned PDFs and images, with pluggable OCR backends.
- **Visual Language Model Support:** Integrates with models like GraniteDocling for enhanced vision-language tasks.
- **CLI and Python API:** Usable both as a command-line tool and as a Python library.

## Installation
```bash
pip install docling
```
- Requires Python >=3.9, <4.0
- For advanced features (OCR, VLM, ASR), install extras: `pip install docling[ocrmac,vlm,asr]`

## Basic Usage Patterns
### Python API Example
```python
from docling.document_converter import DocumentConverter
source = "https://arxiv.org/pdf/2408.09869"  # Local file path or URL
converter = DocumentConverter()
doc = converter.convert(source).document
print(doc.export_to_markdown())
```
- The `convert()` method auto-detects the document type and applies the appropriate backend.
- Output can be exported to Markdown, JSON, or other supported formats.

### CLI Example
```bash
docling https://arxiv.org/pdf/2206.01062
```
- Run `docling --help` for all CLI options.

## Advanced Usage Patterns
- **Batch Processing:** Docling supports batch conversion for high-throughput scenarios.
- **Custom Pipelines:** Users can subclass the model pipeline to add/replace models (e.g., custom OCR, layout analysis, or table recognition).
- **Runtime Configuration:** Options include toggling OCR, setting thread budgets, and choosing PDF backends (native or pypdfium for speed/memory trade-offs).
- **Integration with RAG:** Docling output is optimized for chunking and embedding (see [quackling](https://pypi.org/project/quackling/)), and integrates with LlamaIndex and LangChain for RAG workflows.
- **Extensibility:** New models and pipeline stages can be added by implementing the Python Callable interface and providing them to the pipeline.

## Best Practices
- **Choose Backend Appropriately:** Use the default docling-parse backend for best quality; use pypdfium for speed/memory-constrained environments.
- **Leverage Unified Output:** Always use the `DoclingDocument` structure for downstream tasks to ensure consistency.
- **Enable OCR Only When Needed:** OCR is slower and should be enabled only for scanned or image-based documents.
- **Batch for Throughput:** For large-scale processing, use batch conversion and multi-threading.
- **Integrate with AI Pipelines:** Use Docling’s integrations with LLM frameworks for RAG, QA, and knowledge extraction.
- **Monitor Performance:** Tune thread count and backend selection based on hardware and document type.
- **Stay Updated:** Follow the [GitHub repo](https://github.com/docling-project/docling) for new features, models, and performance improvements.

## Performance Considerations
- **Speed:** Docling is optimized for both batch and interactive use. On modern CPUs, it can process 1–2 pages/sec (PDF, no OCR, default backend).
- **Memory:** The pypdfium backend uses less memory but may reduce quality, especially for tables.
- **OCR Overhead:** Enabling OCR significantly increases processing time (up to 30s/page on CPU).
- **GPU Acceleration:** Some model stages (layout, table recognition) can leverage GPU if available (via ONNXRuntime, PyTorch).

## Common Pitfalls
- **OCR Overuse:** Enabling OCR unnecessarily will slow down processing.
- **Unsupported Formats:** While Docling covers most common formats, some proprietary or highly corrupted files may not parse correctly.
- **Threading Issues:** Over-allocating threads can lead to diminishing returns or resource contention.
- **Custom Model Integration:** Extending the pipeline requires careful adherence to the Callable interface and correct augmentation of page objects.

## Integration with ConversationalBIM
Docling is used in ConversationalBIM for robust document ingestion, conversion, and extraction of structured content from diverse building-related documents. Its unified output enables seamless downstream processing for embedding, chunking, and integration with retrieval-augmented generation (RAG) systems, supporting natural language querying and knowledge extraction from both structured and unstructured sources.

## Resources
- **Official Documentation:** https://docling-project.github.io/docling/
- **GitHub Repository:** https://github.com/docling-project/docling
- **Technical Report:** https://arxiv.org/abs/2408.09869
- **PyPI:** https://pypi.org/project/docling/
- **IBM Research Blog:** https://research.ibm.com/blog/docling-generative-AI
- **Medium Usage Guide:** https://medium.com/mlworks/docling-all-aware-document-parser-7b166ec8a889
- **TableFormer Model:** https://research.ibm.com/publications/tableformer-table-structure-understanding-with-transformers
- **DocLayNet Dataset:** https://research.ibm.com/publications/doclaynet-a-large-human-annotated-dataset-for-document-layout-segmentation

## References
- Auer, C., et al. "Docling Technical Report." arXiv preprint arXiv:2408.09869 (2024).
- IBM Research Blog: "IBM is open-sourcing a new toolkit for document conversion." Nov 2024.
- Docling Official Documentation and GitHub.

---
Compiled: 2025-09-28
