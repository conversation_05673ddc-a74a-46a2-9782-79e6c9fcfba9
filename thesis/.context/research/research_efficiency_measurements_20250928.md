# Efficiency Measurements in Conversational AI, BIM, and Technical HCI Systems (2019–2025)

This research summary synthesizes recent academic work (last 5 years) on efficiency measurement, usability metrics, and comparative studies in conversational AI, Building Information Modeling (BIM), and technical human-computer interaction (HCI) systems, with a focus on Keystroke-Level Model (KLM) and related methodologies.

## 1. Current State of Research

### Conversational AI & Usability
- **Interactive Recommendation Agent with Active User Commands** (<PERSON> et al., 2025): Proposes a dual-agent architecture for natural language recommendation interfaces, evaluated via user satisfaction and business outcomes. Emphasizes real-time linguistic commands for explicit user control and reports significant improvements in user satisfaction through extensive experiments.
- **SciReasoner: Laying the Scientific Reasoning Ground Across Disciplines** (<PERSON> et al., 2025): Introduces a foundation model for scientific reasoning across domains, evaluated on cross-discipline tasks. Focuses on instruction coverage, generalization, and fidelity, with open-sourced evaluation code and datasets.

### HCI & Efficiency Metrics
- **SAGE: A Realistic Benchmark for Semantic Understanding** (<PERSON><PERSON> et al., 2025): Presents a benchmark for evaluating semantic understanding in AI models, including human preference alignment and robustness. Highlights the need for more realistic, adversarial evaluation frameworks that probe deeper aspects of usability and efficiency.
- **Nova: Real-Time Agentic Vision-Language Model Serving** (<PERSON> et al., 2025): Proposes a real-time scheduling framework for vision-language models, optimizing latency and throughput. Evaluates system efficiency using latency and throughput metrics, demonstrating improvements over state-of-the-art baselines.

### Comparative & Usability Studies
- **SD3.5-Flash: Distribution-Guided Distillation of Generative Flows** (Bandyopadhyay et al., 2025): Focuses on efficient deployment of generative AI, with large-scale user studies comparing prompt alignment and generation speed. Demonstrates that pipeline optimizations can yield significant usability and efficiency gains.
- **RLBFF: Binary Flexible Feedback to bridge between Human Feedback & Verifiable Rewards** (Wang et al., 2025): Proposes a new RL paradigm for LLM post-training, combining human and rule-based feedback. Evaluates reward models on alignment and response quality, with metrics for performance and interpretability.

### BIM & Technical HCI
- **Efficient Digital Methods to Quantify Sensor Output Uncertainty** (Kaparounakis et al., 2025): While not BIM-specific, this work addresses efficiency in technical systems, focusing on uncertainty quantification and its impact on system performance and usability.

## 2. Major Approaches and Methodologies
- **User Studies & Satisfaction Metrics**: Many recent works employ large-scale user studies, satisfaction surveys, and business outcome metrics to evaluate conversational and recommendation systems.
- **Benchmarking & Comparative Evaluation**: New benchmarks (e.g., SAGE) and comparative studies are increasingly used to probe robustness, semantic understanding, and efficiency under realistic conditions.
- **System Performance Metrics**: Latency, throughput, and resource utilization are common quantitative metrics for technical HCI and AI systems.
- **Reward Modeling & Feedback**: RL-based approaches are evaluated using both human and verifiable feedback, with new metrics for alignment and interpretability.

## 3. Key Findings
- There is a strong trend toward **realistic, user-centered evaluation** in conversational AI and technical HCI, with large-scale user studies and benchmarks that reflect real-world conditions.
- **Efficiency metrics** (latency, throughput, prompt alignment) are increasingly important for both system and user experience evaluation.
- **Comparative studies** reveal that no single approach excels across all usability and efficiency dimensions, highlighting trade-offs and the need for multi-faceted evaluation.
- **Direct KLM-based or BIM-specific usability studies remain rare** in recent literature; most efficiency work focuses on general HCI or AI system performance.

## 4. Research Gaps Identified
- **Lack of KLM and classical HCI metrics** in recent conversational AI and BIM usability studies. Most recent work uses user satisfaction and system performance metrics, with little adoption of KLM or similar formal models.
- **Scarcity of BIM-specific usability/efficiency studies** using standardized HCI methodologies.
- **Need for comparative studies** directly contrasting conversational interfaces with traditional (graph/table) interfaces in technical domains like BIM.

## 5. Opportunities for ConversationalBIM
- **Apply KLM and formal HCI evaluation** to conversational BIM interfaces, filling a gap in current research.
- **Conduct comparative studies** between conversational and traditional BIM interfaces using both classical (KLM, SUS) and modern (user satisfaction, latency) metrics.
- **Develop new benchmarks** for BIM-specific conversational tasks, leveraging lessons from SAGE and similar frameworks.

---

# References
See `thesis/.context/papers/paper_links.md` for full paper list and download links.

---

*Prepared: 2025-09-28*
