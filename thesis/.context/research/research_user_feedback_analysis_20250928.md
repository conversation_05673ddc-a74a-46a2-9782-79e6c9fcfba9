# User Feedback Analysis Methodologies in Technical HCI and Conversational AI Systems (2019–2025)

## Overview
This summary synthesizes recent academic work (2019–2025) on user feedback analysis methodologies in technical HCI and conversational AI systems, focusing on both qualitative and quantitative methods, coding schemes, and best practices. The review draws on systematic literature reviews, empirical studies, and technical papers from top venues in HCI and AI.

## Key Papers Reviewed
- <PERSON> et al. (2022): UX research on conversational human-AI interaction [ACM DL]
- Tumenjargal & Balan (2024): Requirements elicitation from user feedback using real-time conversational AI [GUPEA]
- <PERSON><PERSON><PERSON> et al. (2022): Review of conversational agent HCI research [JAIS]
- <PERSON><PERSON> et al. (2023): Survey of user studies for model explanations [IEEE Xplore]
- Izadi & Forouzanfar (2024): Error correction and adaptation in conversational AI [MDPI]
- <PERSON><PERSON> et al. (2025): User preferences in conversational AI for healthcare [ACM DL]
- <PERSON> et al. (2025): RLBFF: Binary Flexible Feedback for LLMs [arXiv]
- <PERSON> et al. (2025): Interactive Recommendation Agent with Active User Commands [arXiv]

## Qualitative Feedback Analysis Methods
- **Thematic Analysis**: Widely used for open-ended user feedback (e.g., interviews, think-aloud protocols, chat logs). Researchers code responses into themes or categories, often using grounded theory or inductive approaches (<PERSON>i et al., 2025; Diederich et al., 2022).
- **Grounded Theory**: Used to develop coding schemes directly from user data, especially in exploratory studies (Zheng et al., 2022).
- **Content Analysis**: Systematic coding of user comments, ratings, or logs to quantify qualitative data (Rong et al., 2023).
- **Best Practices**:
  - Use multiple coders and inter-rater reliability checks (Cohen’s kappa, Fleiss’ kappa).
  - Iteratively refine codebooks and resolve disagreements through discussion.
  - Triangulate qualitative findings with quantitative data when possible.

## Quantitative Feedback Analysis Methods
- **Surveys and Questionnaires**: Standardized instruments (e.g., SUS, NASA-TLX, custom Likert scales) to quantify user satisfaction, usability, and trust (Diederich et al., 2022).
- **Behavioral Metrics**: Log analysis of user interactions (e.g., click counts, completion times, error rates) for objective measurement (Tang et al., 2025).
- **Statistical Analysis**: Use of descriptive and inferential statistics (t-tests, ANOVA, regression) to compare groups or conditions (Tumenjargal & Balan, 2024).
- **Binary Coding of Feedback**: Recent work (Wang et al., 2025) proposes coding natural language feedback into binary principles (e.g., yes/no for accuracy, helpfulness) to enable quantitative reward modeling in LLMs.

## Coding Schemes
- **Inductive Coding**: Codes/themes emerge from the data (bottom-up), suitable for exploratory studies.
- **Deductive Coding**: Predefined codes based on theory or prior work (top-down), useful for hypothesis-driven research.
- **Hybrid Approaches**: Combine inductive and deductive methods for comprehensive coverage.
- **Binary Coding**: For reward modeling and automated analysis, feedback is mapped to binary outcomes (Wang et al., 2025).
- **Best Practices**:
  - Document coding rules and provide examples for each code.
  - Use software tools (NVivo, Atlas.ti, MAXQDA) for large datasets.
  - Validate coding schemes with pilot data.

## Best Practices for User Feedback Analysis
- **Triangulation**: Combine qualitative and quantitative methods for robust insights.
- **Iterative Analysis**: Refine coding and analysis as new data emerges.
- **Transparency**: Report coding procedures, reliability metrics, and limitations.
- **User-Centered Design**: Involve users in feedback interpretation and system refinement (Joshi et al., 2025).
- **Ethical Considerations**: Ensure privacy, informed consent, and responsible data handling.

## Emerging Trends
- **Conversational Feedback Collection**: Real-time chatbots and conversational agents are increasingly used to elicit user feedback, enabling richer and more contextual responses (Tumenjargal & Balan, 2024).
- **Automated Coding and Sentiment Analysis**: Use of NLP and ML techniques to automate coding of large-scale feedback (Izadi & Forouzanfar, 2024).
- **Personalized Feedback Loops**: Adaptive systems that adjust based on ongoing user feedback (Tang et al., 2025).
- **Explainable AI Feedback**: Special coding schemes for user feedback on model explanations and transparency (Rong et al., 2023).

## Research Gaps Identified
- Lack of standardized coding schemes for feedback in conversational AI.
- Limited integration of qualitative and quantitative feedback in technical HCI evaluation.
- Need for scalable, automated analysis methods for large-scale conversational logs.
- Underexplored user feedback analysis in domain-specific technical applications (e.g., BIM, engineering tools).

## References
See `thesis/.context/papers/paper_links.md` for full paper details and links.
