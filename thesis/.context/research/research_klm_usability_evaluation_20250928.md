# Keystroke-Level Model (KLM) Usability Evaluation for Conversational AI and Technical Interfaces

## 1. Introduction

The Keystroke-Level Model (KLM) is a predictive model from the GOMS family, originally developed by <PERSON>, <PERSON>, and <PERSON><PERSON> (1980), to estimate the time an expert user needs to complete tasks using interactive systems. KLM decomposes user actions into low-level operators (keystrokes, mouse movements, mental preparation, etc.), assigning standard times to each, and summing them to predict task completion time. This approach has been widely used to evaluate and compare the efficiency of traditional interfaces, such as form-based and graph-based UIs. Its application to conversational AI interfaces—especially in technical domains like BIM and information retrieval—is a recent and evolving area of research.

## 2. KLM Methodology: Fundamentals and Extensions

- **Core KLM Operators:** KLM models tasks as sequences of primitive actions: keystrokes (K), pointing (P), homing (H), drawing (D), mental preparation (M), and system response (R). Each operator has a standard time, e.g., K ≈ 0.2s, M ≈ 1.35s.
- **Assumptions:** KLM assumes expert, error-free performance and focuses on routine, well-learned tasks.
- **Extensions:** Recent work extends KLM to mobile, touch, and multimodal interfaces (<PERSON><PERSON><PERSON> et al., 2007; Al, 2018), and adapts it for accessibility (<PERSON><PERSON><PERSON> et al., 2010; Al, 2017).
- **Validation:** Systematic reviews (Al, 2018) highlight the need for empirical validation and adaptation of KLM for new interaction paradigms.

## 3. KLM for Conversational AI Interfaces

- **Direct Application:** KLM has been applied to chatbot and conversational agent interfaces, modeling user input as sequences of keystrokes and mental operators (Prahara & Saputro, 2019).
- **Unique Challenges:** Conversational interfaces introduce variability in input length, cognitive load, and system response times. Unlike form-based UIs, users may compose longer, less-structured queries, requiring more mental preparation (M) and editing (K).
- **Comparative Studies:** Studies comparing chatbots to traditional forms (Prahara & Saputro, 2019) show that conversational interfaces can reduce navigation and search time but may increase input time due to longer utterances.
- **Technical Domains:** In technical domains (e.g., BIM, information retrieval), conversational interfaces can lower the barrier for non-expert users, but efficiency gains depend on query complexity and system intelligence (Freed, 2021; Diederich et al., 2022).

## 4. KLM for Graph-Based and Form-Based Interfaces

- **Graph-Based UIs:** KLM can model graph navigation as a sequence of pointing, clicking, and mental preparation steps. Complexity increases with graph size and depth.
- **Form-Based UIs:** Well-structured forms are amenable to KLM analysis, with predictable sequences of keystrokes and pointing.
- **Comparative Findings:** Traditional UIs often outperform conversational interfaces for simple, repetitive tasks, while conversational UIs excel in complex, multi-step queries (Prahara & Saputro, 2019).

## 5. Best Practices for KLM Usability Evaluation

- **Task Decomposition:** Clearly define and decompose tasks into KLM operators for each interface type.
- **Expert User Assumption:** Ensure tasks are well-learned and users are familiar with the interface.
- **Empirical Calibration:** Adjust operator times based on empirical measurements for the specific user population and device.
- **Scenario Selection:** Include both simple and complex tasks to capture the strengths and weaknesses of each interface.
- **Cognitive Load:** Account for increased mental preparation in conversational interfaces.
- **System Response:** Include realistic system response times (R) for conversational AI, which may vary more than in traditional UIs.

## 6. Common Pitfalls

- **Ignoring Input Variability:** Conversational input length and complexity can vary widely; using average values may obscure important differences.
- **Overlooking Cognitive Load:** Conversational tasks often require more mental effort, which should be reflected in the number and duration of M operators.
- **Neglecting Error Handling:** KLM assumes error-free performance, but conversational interfaces may induce more corrections and clarifications.
- **Lack of Realism:** Failing to model realistic system response times or user behaviors can lead to inaccurate predictions.

## 7. Recent Advances

- **KLM Extensions for Multimodal and Mobile:** New models incorporate touch, speech, and accessibility features (Al, 2017; Holleis et al., 2007).
- **Empirical Validation:** Studies increasingly combine KLM with user studies to validate predictions (Al, 2018).
- **Conversational AI Evaluation:** Recent work (Freed, 2021; Diederich et al., 2022) explores hybrid evaluation frameworks, combining KLM with qualitative and cognitive measures.
- **Technical Domain Applications:** Early studies in technical domains (e.g., BIM, information retrieval) suggest that conversational interfaces can improve accessibility and reduce training time, but efficiency gains are task-dependent.

## 8. Research Gaps and Opportunities

- **Lack of Standardized KLM Extensions for Conversational AI:** No widely adopted KLM variant exists for conversational interfaces in technical domains.
- **Cognitive Load Modeling:** More research is needed to accurately model and measure the cognitive demands of conversational tasks.
- **Error and Correction Modeling:** Incorporating error rates and correction behaviors into KLM for conversational UIs remains an open challenge.
- **Empirical Benchmarking:** Few large-scale, empirical studies compare conversational and traditional interfaces using KLM in technical settings.

## 9. References and Further Reading

- Card, S.K., Moran, T.P., & Newell, A. (1980). The keystroke-level model for user performance time with interactive systems. [ACM](https://dl.acm.org/doi/pdf/10.1145/358886.358895)
- Holleis, P., Otto, F., Hussmann, H., & Schmidt, A. (2007). Keystroke-level model for advanced mobile phone interaction. [ACM](https://dl.acm.org/doi/abs/10.1145/1240624.1240851)
- Al, S. (2018). A Systematic Review of Modifications and Validation Methods for the Extension of the Keystroke‐Level Model. [Wiley](https://onlinelibrary.wiley.com/doi/abs/10.1155/2018/7528278)
- Prahara, A., & Saputro, T.S. (2019). Keystroke-level model to evaluate chatbot interface for reservation system. [IEEE](https://ieeexplore.ieee.org/abstract/document/8977029/)
- Trewin, S., John, B.E., Richards, J., & Swart, C. (2010). Towards a tool for keystroke level modeling of skilled screen reading. [ACM](https://dl.acm.org/doi/abs/10.1145/1878803.1878811)
- Freed, A. (2021). Conversational AI. [Google Books](https://books.google.com/books?hl=en&lr=&id=XN9BEAAAQBAJ&oi=fnd&pg=PA1&dq=KLM+evaluation+conversational+AI&ots=1-CQg4XfZJ&sig=fBuUOPc66NbyI8qr3PPYw6y1Nag)
- Diederich, S., Brendel, A.B., Morana, S., et al. (2022). On the design of and interaction with conversational agents: An organizing and assessing review of human-computer interaction research. [AISel](https://aisel.aisnet.org/jais/vol23/iss1/9/)

---

**Prepared: 2025-09-28**

---

### Appendix: Additional Resources
- [Blind flm: An enhanced keystroke-level model for visually impaired smartphone interaction](https://link.springer.com/chapter/10.1007/978-3-319-67744-6_10)
- [Keystroke-level analysis of Korean text entry methods on mobile phones](https://www.sciencedirect.com/science/article/pii/S1071581903001757)
- [Using keystrokes to predict social dynamics in dialogue](https://adamgoodkind.com/files/Prospectus.pdf)
- [Conversational Interfaces - Literature Review](https://www.alexandria.unisg.ch/entities/publication/8880ec95-f7b6-4623-96ad-aebb1e3f4a53)
