# KLM, HCI Evaluation, and Usability Testing Protocols: Key Papers (2018–2025)

## Keystroke Level Model (KLM) Methodology

- Card, S.K<PERSON>, <PERSON>, T.P., & <PERSON>, A. (1980). The keystroke-level model for user performance time with interactive systems. [ACM DL](https://dl.acm.org/doi/pdf/10.1145/358886.358895)
- <PERSON><PERSON>, D<PERSON> (2001). Using the keystroke-level model to estimate execution times. [PDF](https://www.cs.loyola.edu/~lawrie/CS774/S06/homework/klm.pdf)
- <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2007). Keystroke-level model for advanced mobile phone interaction. [ACM DL](https://dl.acm.org/doi/abs/10.1145/1240624.1240851)
- Al, S. (2018). A Systematic Review of Modifications and Validation Methods for the Extension of the Keystroke‐Level Model. [Wiley](https://onlinelibrary.wiley.com/doi/abs/10.1155/2018/7528278)
- <PERSON>, <PERSON>, & <PERSON>, M<PERSON> (2014). Enhancing KLM (keystroke-level model) to fit touch screen mobile devices. [ACM DL](https://dl.acm.org/doi/abs/10.1145/2628363.2628385)

## Human-Computer Interaction (HCI) Evaluation

- Frey, J., Mühl, C., Lotte, F., & Hachet, M. (2013). Review of the use of electroencephalography as an evaluation method for human-computer interaction. [arXiv](https://arxiv.org/abs/1311.2222)
- Carvalho, R.M., de Castro Andrade, R.M., et al. (2017). Quality characteristics and measures for human–computer interaction evaluation in ubiquitous systems. [Springer](https://link.springer.com/article/10.1007/s11219-016-9320-z)
- Wobbrock, J.O., & Kientz, J.A. (2016). Research contributions in human-computer interaction. [ACM DL](https://dl.acm.org/doi/abs/10.1145/2907069)
- Day, J.A., & Foley, J.D. (2006). Evaluating a web lecture intervention in a human–computer interaction course. [IEEE Xplore](https://ieeexplore.ieee.org/abstract/document/4012664/)
- Song, X., Liu, M., Gong, L., Gu, Y., & Shidujaman, M. (2023). A review of human-computer interface evaluation research based on evaluation process elements. [Springer](https://link.springer.com/chapter/10.1007/978-3-031-35596-7_17)

## Usability Testing Protocols

- Krahmer, E., & Ummelen, N. (2004). Thinking about thinking aloud: A comparison of two verbal protocols for usability testing. [IEEE Xplore](https://ieeexplore.ieee.org/abstract/document/1303808/)
- Dumas, J. (2001). Usability testing methods: Think aloud protocols. [ResearchGate](https://www.researchgate.net/profile/Joseph-Dumas/publication/341494415_Usability_testing_methods_Think_aloud_protocols_In_R_Branaghan_Ed_Design_by_people_for_people_Essays_on_usability_Chicago_Usability_Professional's_Association_119-130/links/5ec416cc299bf1c09acbc99e/Usability-testing-methods-Think-aloud-protocols-In-R-Branaghan-Ed-Design-by-people-for-people-Essays-on-usability-Chicago-Usability-Professionals-Association-119-130)
- Olmsted-Hawala, E.L. (2010). Think-aloud protocols: a comparison of three think-aloud protocols for use in testing data-dissemination web sites for usability. [ACM DL](https://dl.acm.org/doi/abs/10.1145/1753326.1753685)
- Cooke, L. (2010). Assessing concurrent think-aloud protocol as a usability test method: A technical communication approach. [IEEE Xplore](https://ieeexplore.ieee.org/abstract/document/5556472/)
- Van den Haak, M.J., De Jong, M.D.T. (2003). Exploring two methods of usability testing: concurrent versus retrospective think-aloud protocols. [IEEE Xplore](https://ieeexplore.ieee.org/abstract/document/1245501/)


## Efficiency Measurements in Conversational AI, BIM, and Technical HCI Systems (2019–2025)

### Conversational AI & Usability
- **Interactive Recommendation Agent with Active User Commands**  
  Authors: <AUTHORS>
  arXiv: [2509.21317v1](http://arxiv.org/abs/2509.21317v1)  
  [PDF](http://arxiv.org/pdf/2509.21317v1)

- **SciReasoner: Laying the Scientific Reasoning Ground Across Disciplines**  
  Authors: <AUTHORS>
  arXiv: [2509.21320v1](http://arxiv.org/abs/2509.21320v1)  
  [PDF](http://arxiv.org/pdf/2509.21320v1)

### HCI & Efficiency Metrics
- **SAGE: A Realistic Benchmark for Semantic Understanding**  
  Authors: <AUTHORS>
  arXiv: [2509.21310v1](http://arxiv.org/abs/2509.21310v1)  
  [PDF](http://arxiv.org/pdf/2509.21310v1)

- **Nova: Real-Time Agentic Vision-Language Model Serving with Adaptive Cross-Stage Parallelization**  
  Authors: <AUTHORS>
  arXiv: [2509.21301v1](http://arxiv.org/abs/2509.21301v1)  
  [PDF](http://arxiv.org/pdf/2509.21301v1)

### Comparative & Usability Studies
- **SD3.5-Flash: Distribution-Guided Distillation of Generative Flows**  
  Authors: <AUTHORS>
  arXiv: [2509.21318v1](http://arxiv.org/abs/2509.21318v1)  
  [PDF](http://arxiv.org/pdf/2509.21318v1)

- **RLBFF: Binary Flexible Feedback to bridge between Human Feedback & Verifiable Rewards**  
  Authors: <AUTHORS>
  arXiv: [2509.21319v1](http://arxiv.org/abs/2509.21319v1)  
  [PDF](http://arxiv.org/pdf/2509.21319v1)

### BIM & Technical HCI (No direct KLM/BIM, but related HCI/efficiency)
- **Efficient Digital Methods to Quantify Sensor Output Uncertainty**  
  Authors: <AUTHORS>
  arXiv: [2509.21311v1](http://arxiv.org/abs/2509.21311v1)  
  [PDF](http://arxiv.org/pdf/2509.21311v1)

---

## User Feedback Analysis in Technical HCI and Conversational AI (2019–2025)

- **UX research on conversational human-AI interaction: A literature review of the ACM digital library**
  - Zheng, Q. et al. (2022)
  - [ACM DL](https://dl.acm.org/doi/abs/10.1145/3491102.3501855)

- **Requirements Elicitation From User Feedback Using Real-Time Conversational AI**
  - Tumenjargal, A. & Balan, S. (2024)
  - [University of Gothenburg](https://gupea.ub.gu.se/handle/2077/84482)

- **On the design of and interaction with conversational agents: An organizing and assessing review of human-computer interaction research**
  - Diederich, S. et al. (2022)
  - [JAIS](https://aisel.aisnet.org/jais/vol23/iss1/9/)

- **Towards human-centered explainable AI: A survey of user studies for model explanations**
  - Rong, Y. et al. (2023)
  - [IEEE Xplore](https://ieeexplore.ieee.org/abstract/document/10316181/)

- **Error correction and adaptation in conversational AI: a review of techniques and applications in chatbots**
  - Izadi, S. & Forouzanfar, M. (2024)
  - [MDPI](https://www.mdpi.com/2673-2688/5/2/41)

- **User Preferences in Conversational AI for Healthcare: Insights from an Interview Study**
  - Joshi, R. et al. (2025)
  - [ACM DL](https://dl.acm.org/doi/abs/10.1145/3719160.3736631)

---

# Paper Links: Evaluation Results Analysis in Conversational AI and BIM Systems (2019-2025)

## Conversational AI

- **SciReasoner: Laying the Scientific Reasoning Ground Across Disciplines**
  - Authors: <AUTHORS>
  - arXiv: [2509.21320v1](http://arxiv.org/abs/2509.21320v1)
  - PDF: [http://arxiv.org/pdf/2509.21320v1](http://arxiv.org/pdf/2509.21320v1)

- **RLBFF: Binary Flexible Feedback to bridge between Human Feedback & Verifiable Rewards**
  - Authors: <AUTHORS>
  - arXiv: [2509.21319v1](http://arxiv.org/abs/2509.21319v1)
  - PDF: [http://arxiv.org/pdf/2509.21319v1](http://arxiv.org/pdf/2509.21319v1)

- **SAGE: A Realistic Benchmark for Semantic Understanding**
  - Authors: <AUTHORS>
  - arXiv: [2509.21310v1](http://arxiv.org/abs/2509.21310v1)
  - PDF: [http://arxiv.org/pdf/2509.21310v1](http://arxiv.org/pdf/2509.21310v1)

- **Interactive Recommendation Agent with Active User Commands**
  - Authors: <AUTHORS>
  - arXiv: [2509.21317v1](http://arxiv.org/abs/2509.21317v1)
  - PDF: [http://arxiv.org/pdf/2509.21317v1](http://arxiv.org/pdf/2509.21317v1)

## BIM and Related System Evaluation

- **Bridging AI with BIM: Development and Evaluation of a Conversational Assistant for Real-Time Model Management**
  - Author: D Fernandes de Oliveira
  - [University of Manitoba Thesis](https://mspace.lib.umanitoba.ca/items/60920a54-8a78-43a7-a5a7-fba729f53816)

- **Prototyping a Chatbot for Site Managers using BIM and NLU Techniques**
  - Author: WY Lin
  - [MDPI Sensors](https://www.mdpi.com/1424-8220/23/6/2942)

- **Integrating BIM with ChatGPT, Bard, and Similar Generative AI**
  - Authors: <AUTHORS>
  - [SSRN Paper](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=4645601)

- **Intelligent Q&A System for BIM and AIoT using BERT**
  - Authors: <AUTHORS>
  - [ScienceDirect](https://www.sciencedirect.com/science/article/pii/S0926580522003569)

- **BIM and AI in Early Design Stage: Advancing Architect–Client Communication**
  - Authors: <AUTHORS>
  - [MDPI Buildings](https://www.mdpi.com/2075-5309/15/12/1977)

- **The Rise of Foundation Models in Industry: A Cross-Domain Survey of LLM Applications**
  - Author: CR Bogadi
  - [ResearchGate](https://www.researchgate.net/profile/Chaithanya-Reddy-Bogadi/publication/394351257_The_Rise_of_Foundation_Models_in_Industry_A_Cross-_Domain_Survey_of_LLM_Applications_in_Healthcare_Finance_Legal_and_Education/links/68939f73c345306d43cbbc3b/The-Rise-of-Foundation-Models-in-Industry-A-Cross-Domain-Survey-of-LLM-Applications-in-Healthcare-Finance-Legal-and-Education.pdf)

- **Large Language Models as Software Components: A Taxonomy for LLM-Integrated Applications**
  - Author: I Weber
  - [arXiv:2406.10300](https://arxiv.org/abs/2406.10300)

- **An Empirical Study on Challenges for LLM Application Developers**
  - Authors: <AUTHORS>
  - [ACM DL](https://dl.acm.org/doi/abs/10.1145/3715007)

- **OpenAGI: When LLM Meets Domain Experts**
  - Authors: <AUTHORS>
  - [NeurIPS Proceedings](https://proceedings.neurips.cc/paper_files/paper/2023/hash/1190733f217404edc8a7f4e15a57f301-Abstract-Datasets_and_Benchmarks.html)

- **Cross-Domain Applications of LLM-Based Retrieval and Dialogue Systems**
  - Author: A Birzoim
  - [TechRxiv](https://www.techrxiv.org/doi/full/10.36227/techrxiv.*********.57191866)

- **The Relationship between AI and BIM Technologies for Sustainable Building**
  - Authors: <AUTHORS>
  - [MDPI Sustainability](https://www.mdpi.com/2071-1050/16/24/10848)

- **Building Information Modelling, Artificial Intelligence and Construction Tech**
  - Authors: <AUTHORS>
  - [ScienceDirect](https://www.sciencedirect.com/science/article/pii/S2666165920300077)

- **Exploring the Intersection of BIM and AI in Modern Infrastructure Projects**
  - Authors: <AUTHORS>
  - [ResearchGate](https://www.researchgate.net/profile/Ifechukwu-Gil-Ozoudeh/publication/387057934_Exploring_the_Intersection_of_Building_Information_Modeling_BIM_and_Artificial_Intelligence_in_Modern_Infrastructure_Projects/links/675dca998a08a27dd0c2f1e5/Exploring-the-Intersection-of-Building-Information-Modeling-BIM-and-Artificial-Intelligence-in-Modern-Infrastructure-Projects.pdf)

- **Towards a Model-Driven Approach for Multiexperience AI-Based User Interfaces**
  - Authors: <AUTHORS>
  - [SpringerLink](https://link.springer.com/article/10.1007/s10270-021-00904-y)


- **No highly relevant BIM-specific evaluation analysis papers found in this batch.**
  - Recommendation: Conduct targeted Google Scholar search and/or check domain-specific venues (e.g., Automation in Construction, Advanced Engineering Informatics, ISARC, CIB W78).

---
*Last updated: 2025-09-28*
