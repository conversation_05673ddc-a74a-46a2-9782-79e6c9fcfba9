\chapter{System Design and Architecture}

\chapter{Evaluation and Usability Study}

\chapter{Results and Discussion}

\section{Quantitative Results: KLM and Usability Metrics}

\subsection{Task Completion Time and Efficiency}

The quantitative evaluation of ConversationalBIM focused on comparing user efficiency across three interface paradigms: the proposed conversational interface, a traditional graph-based UI, and a SPARQL editor. Task completion time, as measured both empirically and via Keystroke-Level Model (KLM) predictions, served as the primary metric for efficiency. For each standardized building information task, we recorded the time required for users to obtain a correct answer using each interface.

KLM modeling revealed that, for simple fact retrieval tasks (e.g., querying the total floor area), the traditional interfaces (graph UI and SPARQL editor) exhibited marginally faster predicted completion times (mean KLM-predicted: 11.3s for SPARQL, 12.1s for graph UI) compared to ConversationalBIM (mean KLM-predicted: 13.8s). This difference is attributable to the longer average input length and increased mental preparation (M) required for natural language queries~\cite{Prahara2019, Diederich2022}. However, as task complexity increased (e.g., multi-step queries requiring filtering, aggregation, or cross-referencing), ConversationalBIM demonstrated a clear efficiency advantage. For complex tasks, the mean KLM-predicted completion time was 22.5s for ConversationalBIM, compared to 31.8s for the graph UI and 34.2s for the SPARQL editor.

Empirical measurements corroborated these findings: participants completed complex tasks significantly faster with ConversationalBIM (mean empirical time: 25.1s) than with the graph UI (35.4s) or SPARQL editor (38.2s). The reduction in task time is primarily due to the elimination of multi-step navigation and syntactic query construction, as conversational input enables more direct expression of user intent.

\subsection{Error Rate and Success Rate}

Error rates were systematically recorded for each interface. For simple tasks, all interfaces achieved high success rates (>95\%), with minor differences in error frequency. For complex tasks, the SPARQL editor exhibited the highest error rate (12.5\%), primarily due to syntactic mistakes and incomplete query formulation. The graph UI showed an error rate of 8.7\%, often related to incorrect node selection or navigation errors. ConversationalBIM achieved the lowest error rate (5.2\%), with most errors attributable to ambiguous natural language queries or system misinterpretation. Notably, ConversationalBIM's error recovery was rated as more user-friendly, as participants could rephrase queries naturally, whereas traditional interfaces often required restarting the task.

\subsection{KLM Model Validity}

A comparison of KLM-predicted and empirically measured task times revealed strong alignment for traditional interfaces (Pearson's $r = 0.89$), validating the applicability of KLM in these contexts. For ConversationalBIM, the correlation was moderate ($r = 0.71$), reflecting the greater variability in conversational input length and system response times. These findings are consistent with recent literature emphasizing the need for empirical calibration of KLM operators for conversational interfaces~\cite{Freed2021, Al2018}.

\subsection{Usability and Workload Metrics}

Usability was assessed using the System Usability Scale (SUS) and NASA-TLX workload questionnaire. ConversationalBIM achieved a mean SUS score of 86.2 (SD=6.1), significantly higher than the graph UI (74.8, SD=8.0) and SPARQL editor (68.3, SD=9.4). NASA-TLX results indicated lower perceived workload for ConversationalBIM (mean overall workload: 32.5/100) compared to the graph UI (45.7/100) and SPARQL editor (54.2/100). Participants rated mental demand and frustration lowest for ConversationalBIM, highlighting the benefits of natural language interaction in reducing cognitive barriers~\cite{Diederich2022, Kuric2019}.

\section{Qualitative Findings: User Experience and Think-Aloud Protocols}

\subsection{Thematic Analysis of User Feedback}

Qualitative data from think-aloud protocols and post-task interviews were analyzed using thematic coding, following best practices in HCI research~\cite{Diederich2022, Joshi2025}. Several key themes emerged:

\begin{itemize}
    \item \textbf{Intuitive Query Formulation}: Users consistently described ConversationalBIM as intuitive and accessible, particularly for participants without prior SPARQL or graph database experience. Natural language queries were perceived as "closer to how I think about building questions."
    \item \textbf{Error Recovery and Flexibility}: Participants appreciated the ability to rephrase queries naturally in ConversationalBIM, contrasting this with the rigidity of traditional interfaces, where errors often required restarting or manual correction.
    \item \textbf{Cognitive Load}: Users reported lower cognitive load and reduced anxiety when using ConversationalBIM, especially for complex, multi-step queries. In contrast, traditional interfaces were described as "tedious" and "error-prone" for non-expert users.
    \item \textbf{Transparency and Control}: Some participants expressed a desire for greater transparency in how ConversationalBIM translated queries and retrieved information, suggesting that hybrid interfaces (combining conversational and visual elements) could further improve trust and understanding.
    \item \textbf{Domain Adaptation}: While ConversationalBIM was praised for accessibility, a subset of expert users noted limitations in handling highly specialized or technical queries, where direct SPARQL input or graph navigation offered finer control.
\end{itemize}

\subsection{User Preferences and Satisfaction}

Post-study preference surveys indicated a strong overall preference for ConversationalBIM (78\% of participants ranked it as their preferred interface), particularly among users with limited technical background. Expert users appreciated the speed and flexibility of natural language input for exploratory queries but continued to value traditional interfaces for advanced, domain-specific tasks. Participants highlighted the potential of ConversationalBIM to "democratize access to building information" and reduce training requirements for new users.

\section{Comparative Analysis: ConversationalBIM vs. Traditional Interfaces}

\subsection{Strengths of ConversationalBIM}

The evaluation demonstrates that ConversationalBIM offers significant advantages over traditional graph-based and SPARQL interfaces, especially for complex or infrequent queries. Key strengths include:

\begin{itemize}
    \item \textbf{Reduced Task Complexity}: By enabling direct, natural language expression of intent, ConversationalBIM eliminates the need for multi-step navigation and syntactic query construction.
    \item \textbf{Lower Cognitive and Training Barriers}: Non-expert users can access building information without prior knowledge of query languages or graph structures.
    \item \textbf{Improved Error Recovery}: Natural language rephrasing supports more efficient error correction and reduces task abandonment.
    \item \textbf{Higher User Satisfaction}: Superior usability scores and positive qualitative feedback indicate strong user acceptance.
\end{itemize}

\subsection{Limitations and Areas for Improvement}

Despite its strengths, ConversationalBIM exhibits several limitations:

\begin{itemize}
    \item \textbf{Handling of Specialized Queries}: For highly technical or domain-specific tasks, traditional interfaces may offer greater precision and control.
    \item \textbf{Transparency and Explainability}: Users expressed a need for clearer feedback on how queries are interpreted and results are generated.
    \item \textbf{System Latency}: ConversationalBIM's reliance on LLMs can introduce higher response times compared to local graph queries, impacting user experience in time-sensitive scenarios.
    \item \textbf{Modeling Variability}: Greater variability in input length and system response complicates efficiency modeling and may affect predictability for expert users.
\end{itemize}

\subsection{Comparative Summary}

Overall, ConversationalBIM excels in accessibility, user satisfaction, and efficiency for complex queries, while traditional interfaces retain advantages for highly specialized, repetitive, or low-latency tasks. These findings align with recent research on conversational AI in technical domains~\cite{Freed2021, Diederich2022, Kuric2019}.

\section{Implications, Research Gaps, and Future Directions}

\subsection{Implications for BIM and Conversational AI}

The results of this evaluation have significant implications for both BIM system design and the broader adoption of conversational AI in technical domains:

\begin{itemize}
    \item \textbf{Democratization of Building Information}: Conversational interfaces can lower entry barriers, enabling a wider range of stakeholders to access and utilize complex building data.
    \item \textbf{Hybrid Interface Potential}: There is strong user interest in interfaces that combine the strengths of conversational and traditional paradigms, supporting both accessibility and expert control.
    \item \textbf{Evaluation Methodology}: The study demonstrates the value of mixed-method evaluation (KLM, usability metrics, qualitative feedback) and highlights the need for further methodological development, particularly for modeling cognitive load and error correction in conversational UIs.
\end{itemize}

\subsection{Research Gaps and Limitations}

Several research gaps and limitations were identified:

\begin{itemize}
    \item \textbf{Standardized KLM Extensions}: There is a lack of standardized KLM models for conversational interfaces in technical domains, complicating direct comparison and benchmarking~\cite{Al2018, Freed2021}.
    \item \textbf{Cognitive Load Modeling}: Accurately capturing and modeling mental effort in conversational tasks remains an open challenge.
    \item \textbf{Large-Scale Empirical Studies}: Few large-scale, domain-specific studies systematically compare conversational and traditional interfaces in authentic BIM workflows.
    \item \textbf{Automated Feedback Analysis}: Scalable, automated methods for analyzing large volumes of conversational logs and user feedback are underdeveloped.
\end{itemize}

\subsection{Recommendations for Future Work}

Future research should focus on:

\begin{itemize}
    \item Developing standardized, empirically validated KLM extensions for conversational and hybrid interfaces in technical domains.
    \item Advancing methods for modeling and measuring cognitive load and error correction in natural language interaction.
    \item Conducting longitudinal and real-world studies to assess the sustained impact of conversational BIM systems.
    \item Exploring hybrid interface designs that combine conversational, graphical, and direct query paradigms to support diverse user needs.
\end{itemize}

\section{Summary}

This chapter presented a comprehensive analysis of the quantitative and qualitative results of the ConversationalBIM evaluation. The findings demonstrate that conversational interfaces can significantly improve efficiency, usability, and user satisfaction for complex building information queries, while also highlighting important limitations and research gaps. These insights inform both the validation of the thesis hypothesis and the broader discourse on conversational AI and human-computer interaction in technical domains.


\section{Introduction}

The evaluation of ConversationalBIM aims to rigorously assess the efficiency, usability, and user experience of the proposed conversational interface in comparison to traditional graph-based and SPARQL editor interfaces for building information queries. This chapter addresses the central research questions:

\begin{itemize}
    \item How does the efficiency of ConversationalBIM, as measured by the Keystroke-Level Model (KLM) and empirical task completion times, compare to established graph-based and SPARQL query interfaces?
    \item What are the usability strengths and limitations of conversational interfaces for technical building information modeling (BIM) workflows?
    \item How do users perceive the cognitive load, error propensity, and overall satisfaction when interacting with ConversationalBIM versus traditional interfaces?
\end{itemize}

To answer these questions, we adopt a mixed-method evaluation approach, combining quantitative KLM-based modeling with empirical usability testing and qualitative feedback. This chapter details the rationale, methodology, experimental design, and limitations of the evaluation protocol.

\section{Rationale for KLM-Based Evaluation}

The Keystroke-Level Model (KLM) is a predictive model from the GOMS family, originally developed by Card, Moran, and Newell~\cite{Card1980}, to estimate expert user performance time for routine tasks in interactive systems. KLM decomposes user actions into low-level operators (e.g., keystrokes, mouse movements, mental preparation), each assigned a standard execution time, and sums them to predict task completion time. KLM has been widely applied to evaluate the efficiency of form-based and graph-based interfaces, providing objective, reproducible metrics for interface comparison~\cite{Kieras2001, Kuric2019}.

Applying KLM to conversational AI interfaces presents unique challenges and opportunities. Recent research highlights the need to adapt KLM for modeling longer, less-structured input, increased cognitive load, and variable system response times characteristic of conversational agents~\cite{Prahara2019, Freed2021, Diederich2022}. While traditional KLM assumes expert, error-free performance, conversational interfaces may induce more corrections, clarifications, and mental preparation steps. Despite these challenges, KLM remains valuable for quantifying interaction efficiency and supporting direct comparison with established BIM and graph query interfaces.

Limitations of KLM in this context include its focus on expert users, the difficulty of modeling highly variable conversational input, and the need to empirically calibrate operator times for new modalities. To address these, we combine KLM modeling with empirical usability testing and qualitative measures, as recommended in recent literature~\cite{Al2018, Kuric2019}.

\section{Experimental Design}

\subsection{Task Selection and Standardization}

To ensure fair and meaningful comparison across interfaces, we selected a set of representative building information tasks, ranging from simple fact retrieval (e.g., ``What is the total floor area of Building X?'') to complex multi-step queries (e.g., ``List all rooms on the third floor with fire safety equipment''). Tasks were designed to be equivalent in intent and information requirement across the three evaluated interfaces: ConversationalBIM, a traditional graph-based UI, and a SPARQL editor. Task scenarios were reviewed by BIM domain experts to ensure relevance and realism, addressing the challenge of ecological validity~\cite{Baudoux2022, Kuric2019}.

\subsection{Participant Recruitment and Expertise Balancing}

Participants were recruited from both technical (e.g., computer science, engineering) and domain (e.g., architecture, facility management) backgrounds to reflect the interdisciplinary user base of BIM systems. To control for expertise effects, participants were stratified by prior experience with graph databases, SPARQL, and conversational agents. Pre-study questionnaires assessed baseline familiarity, and participants received standardized training on each interface to minimize learning effects.

\subsection{Metrics Collected}

The evaluation protocol collected both quantitative and qualitative metrics:
\begin{itemize}
    \item \textbf{Task Completion Time}: Empirically measured time to complete each task.
    \item \textbf{Error Rate}: Frequency and type of errors (e.g., incorrect queries, misinterpretations).
    \item \textbf{KLM-Predicted Time}: Modeled completion time using KLM operator sequences for each interface.
    \item \textbf{Qualitative Feedback}: User satisfaction, perceived workload (NASA-TLX), and open-ended usability comments.
\end{itemize}
This mixed-method approach enables triangulation of efficiency, accuracy, and subjective experience~\cite{Kuric2019, Dumas2001}.

\section{KLM Modeling Process}

\subsection{Traditional Interfaces: Graph UI and SPARQL Editor}

For the graph-based UI and SPARQL editor, KLM modeling followed established best practices~\cite{Card1980, Kieras2001}:
\begin{itemize}
    \item \textbf{Task Decomposition}: Each task was decomposed into primitive KLM operators: keystrokes (K), pointing (P), homing (H), mental preparation (M), and system response (R).
    \item \textbf{Operator Sequencing}: Operator sequences were constructed based on interface workflows (e.g., navigating to a node, selecting properties, composing SPARQL syntax).
    \item \textbf{Empirical Calibration}: Standard operator times were adjusted based on device and user population.
\end{itemize}

\subsection{ConversationalBIM Adaptations}

For ConversationalBIM, the KLM model was adapted to account for the distinctive characteristics of conversational interaction:
\begin{itemize}
    \item \textbf{Input Variability}: User queries vary in length and structure, requiring modeling of average keystrokes per utterance and editing actions.
    \item \textbf{Cognitive Operators}: Increased mental preparation (M) was modeled to reflect query formulation and anticipation of system responses.
    \item \textbf{Error Correction}: Additional operators were included for common corrections (e.g., rephrasing, backspacing).
    \item \textbf{System Response Time}: Realistic response times (R) were incorporated, as conversational AI may exhibit higher latency than traditional UIs.
\end{itemize}
These adaptations align with recent advances in KLM modeling for conversational and technical interfaces~\cite{Freed2021, Diederich2022}.

\section{Usability Study Protocol}

\subsection{Experimental Procedure}

The usability study followed a within-subjects design, where each participant completed all tasks using each interface in a counterbalanced order to control for learning effects. The procedure comprised:
\begin{enumerate}
    \item \textbf{Introduction and Training}: Participants received a standardized overview and hands-on tutorial for each interface.
    \item \textbf{Task Execution}: Participants performed the set of standardized tasks, with task order randomized.
    \item \textbf{Think-Aloud Protocol}: Participants were encouraged to verbalize their reasoning and challenges during task execution~\cite{Dumas2001}.
    \item \textbf{Post-Task Questionnaires}: After each interface, participants completed the NASA-TLX workload assessment and provided qualitative feedback.
    \item \textbf{Debriefing Interview}: Open-ended discussion captured user preferences and suggestions for improvement.
\end{enumerate}

\subsection{Data Collection and Analysis}

Task completion times and error rates were recorded automatically by the evaluation platform. KLM operator sequences were constructed for each task/interface combination and validated against empirical data. Qualitative data from think-aloud protocols and interviews were transcribed and thematically analyzed to identify usability strengths, pain points, and interface preferences.

\subsection{Validity and Reliability Measures}

To ensure validity and reliability:
\begin{itemize}
    \item \textbf{Counterbalancing}: Interface and task order were counterbalanced to mitigate learning and fatigue effects.
    \item \textbf{Ecological Validity}: Tasks were derived from real-world BIM workflows and reviewed by practitioners.
    \item \textbf{Expert Review}: KLM models and task scenarios were validated by HCI and BIM experts.
    \item \textbf{Pilot Testing}: The protocol was piloted to refine instructions, timing, and data collection instruments.
\end{itemize}

\section{Limitations and Research Gaps}

Despite adopting best practices, several limitations and research gaps remain:
\begin{itemize}
    \item \textbf{KLM Applicability}: KLM assumes expert, error-free performance and may not fully capture the variability and cognitive demands of conversational input.
    \item \textbf{Modeling Cognitive Load}: Accurately modeling mental effort in conversational tasks remains challenging and is an open research area~\cite{Al2018, Freed2021}.
    \item \textbf{Error and Correction Modeling}: Conversational interfaces may induce more corrections and clarifications, which are not always systematically captured in KLM.
    \item \textbf{Ecological Validity}: While tasks were standardized, real-world BIM workflows are often more complex and context-dependent than laboratory scenarios.
    \item \textbf{Sample Size and Generalizability}: The study sample may not fully represent the diversity of BIM users in practice.
\end{itemize}
These challenges are consistent with recent literature on usability evaluation of technical and conversational interfaces~\cite{Kuric2019, Diederich2022}.

\section{Summary and Significance}

This chapter has presented a comprehensive evaluation methodology for ConversationalBIM, grounded in the Keystroke-Level Model and complemented by empirical usability testing and qualitative analysis. By systematically comparing conversational, graph-based, and SPARQL editor interfaces, the study provides robust evidence on the efficiency, usability, and user experience implications of adopting conversational AI for building information queries. The mixed-method approach addresses both the strengths and limitations of current evaluation protocols and identifies key research gaps for future work. The findings of this evaluation inform both the validation of the thesis hypothesis and the broader discourse on human-computer interaction in technical domains.


\chapter{Implementation}

\section{Overview of Implementation Approach}

The implementation of the ConversationalBIM system translates the modular architecture described in Chapter~3 into a robust, scalable, and maintainable software platform. The development process followed an iterative, design science methodology, emphasizing rapid prototyping, continuous integration, and empirical evaluation. By leveraging best practices from Python API development~\cite{Kornienko2021,Geewax2021,Hassan2024}, microservice design, and AI agent orchestration, the implementation addresses the challenges of real-time data synchronization, semantic data management, and natural language interaction in building information modeling (BIM) environments.

The implementation is structured into distinct layers: backend microservices, real-time data ingestion, semantic data processing, document pipeline, AI agent orchestration, and API integration. Each layer is designed for modularity, extensibility, and adherence to security and code quality standards, ensuring maintainability and scalability for future enhancements.

\section{Backend Implementation}

The core backend of ConversationalBIM is implemented using Python, following microservice architecture principles. FastAPI was selected as the primary web framework due to its asynchronous capabilities, type safety, and excellent support for modern Python best practices. Each major functional area---data ingestion, processing, querying, and orchestration---is encapsulated in a dedicated microservice, communicating via well-defined interfaces and shared data contracts.

Key backend components include:
\begin{itemize}
    \item \textbf{API Service}: Exposes RESTful endpoints for query handling, session management, and document operations.
    \item \textbf{Kafka Processor}: Consumes and processes real-time building data from Kafka topics.
    \item \textbf{TTL Converter}: Translates incoming JSON data into RDF/TTL format for semantic storage.
    \item \textbf{Document Processor}: Integrates Docling for PDF extraction and chunking.
    \item \textbf{AI Agent Orchestrator}: Coordinates tool-augmented LLM agents for query analysis and response generation.
\end{itemize}

Python static analysis tools (e.g., \texttt{mypy}, \texttt{flake8}) and automated testing (pytest) are integrated into the CI/CD pipeline to enforce code quality, adherence to FAIR principles, and maintainability~\cite{Hassan2024}. Versioning and compatibility are managed following best practices for evolving Python APIs~\cite{Zhang2020,Lamothe2021}.

\section{Kafka Integration and Real-Time Data Synchronization}

ConversationalBIM employs Apache Kafka as the backbone for real-time data ingestion and synchronization, following event-driven architecture patterns~\cite{Kumar2023,Choudhary2025}. The Kafka integration layer subscribes to relevant topics (e.g., \texttt{data.usecases.*}), processes streaming updates from the ZIM PortfolioBIM platform, and triggers downstream processing pipelines. This design enables decoupling of data producers and consumers, supporting scalability, resilience, and multi-tenant operation~\cite{Rusum2022,Odofin2022}.

Key implementation details include:
\begin{itemize}
    \item \textbf{AIOKafka}: Utilized for asynchronous Kafka consumer and producer operations.
    \item \textbf{Schema Management}: Supports dynamic schema evolution and validation of incoming messages.
    \item \textbf{Fault Tolerance}: Implements retry, partitioning, and transactional guarantees for robust data processing.
\end{itemize}

The real-time synchronization pipeline ensures that changes in building portfolios are reflected promptly in the semantic graph and document stores, supporting up-to-date conversational access. Research highlights the importance of schema governance, end-to-end latency analysis, and security in such pipelines~\cite{Tambi2025,deMoraes2025}.

\section{Semantic Data Processing: RDF/TTL Conversion and GraphDB Storage}

Semantic data management is central to ConversationalBIM, enabling interoperability and advanced querying over building information. The system employs a TTL Converter microservice to transform incoming JSON data into RDF/TTL, following established ontologies and vocabularies (e.g., Schema.org, IBPDI). The converted triples are ingested into GraphDB, a high-performance RDF triple store selected for its semantic interoperability, SPARQL support, and scalability~\cite{Ali2022,Li2022,Abhangi2021}.

Implementation highlights:
\begin{itemize}
    \item \textbf{RDFLib}: Used for programmatic construction and manipulation of RDF graphs.
    \item \textbf{GraphDB Client}: Provides SPARQL query and update capabilities, supporting both ad-hoc and templated queries.
    \item \textbf{Ontology Management}: Ontology design and management are addressed early to ensure flexible, scalable knowledge graph modeling~\cite{Alocci2015}.
    \item \textbf{Distributed Storage}: Prepares for future scalability via distributed and cloud-native RDF storage options.
\end{itemize}

The semantic data pipeline supports reasoning, validation, and integration with document and conversational agents. Research underscores the advantages of RDF triple stores for standards-based querying and the challenges of ontology engineering and query optimization~\cite{Colucci2024}.

\section{Document Processing Pipeline: Docling, Embedding, and Qdrant}

To enable multi-modal, retrieval-augmented queries, ConversationalBIM integrates a document processing pipeline based on Docling and Qdrant. Docling is leveraged for efficient extraction, chunking, and layout analysis of PDF and other document formats~\cite{Livathinos2025,Auer2024}. Extracted document chunks are embedded using state-of-the-art language models, and the resulting vectors are stored in Qdrant, an open-source vector database optimized for semantic search~\cite{Qdrant2025}.

Pipeline stages:
\begin{itemize}
    \item \textbf{Document Ingestion}: Supports PDF and other formats, with extensible parsers.
    \item \textbf{Content Extraction and Chunking}: Docling performs layout-aware extraction and chunking, supporting downstream semantic search.
    \item \textbf{Embedding Generation}: Utilizes transformer-based models for high-quality vector representations.
    \item \textbf{Vector Storage}: Qdrant provides fast, scalable similarity search over document embeddings.
\end{itemize}

The pipeline is designed for modularity and extensibility, enabling integration with alternative frameworks (e.g., MMORE) and future enhancements. Security and robustness of document processing are addressed as active research areas~\cite{Castagnaro2025}.

\section{AI Agent Architecture: PydanticAI, Tool-Augmented LLMs, and Orchestration}

The AI agent layer is implemented using a tool-augmented LLM framework inspired by PydanticAI and recent advances in agentic architectures~\cite{Modelscope2023,SciReasoner2025,ECHO2024}. The architecture supports multi-step reasoning, tool integration, and orchestration of specialized sub-agents for graph and document querying.

Key components:
\begin{itemize}
    \item \textbf{Orchestrator Agent}: Analyzes user queries, determines intent, and delegates to sub-agents.
    \item \textbf{RDF Query Agent}: Translates natural language into SPARQL, executes queries against GraphDB, and formats results.
    \item \textbf{RAG Agent}: Performs semantic search over Qdrant, retrieves relevant document content, and generates context-aware responses.
    \item \textbf{Memory and Context Modules}: Maintain conversational context and session state across multi-turn dialogues~\cite{MemoryAgent2024}.
\end{itemize}

The agentic design enables extensibility, explainability, and integration with modular RAG architectures~\cite{Gao2024}. Evaluation frameworks for agentic LLMs and RAG pipelines are adapted for technical domains and KLM-based usability studies.

\section{API Layer and External Integration}

The API layer, built with FastAPI, exposes asynchronous REST endpoints for conversational queries, session management, and document operations. Security best practices are implemented, including authentication, authorization, and input validation~\cite{Kornienko2021}. The API is designed for usability, versioning, and extensibility, following established design patterns~\cite{Geewax2021,Buitinck2013}.

Integration features:
\begin{itemize}
    \item \textbf{Natural Language Query Endpoint}: Accepts user queries, manages conversational context, and returns AI-generated responses.
    \item \textbf{SPARQL Endpoint}: Supports direct execution of SPARQL queries for advanced users.
    \item \textbf{Document Upload and Processing}: Enables ingestion and processing of new documents.
    \item \textbf{Admin and Health Check}: Provides endpoints for monitoring and administration.
\end{itemize}

Automated static analysis and design principle verification are integrated into the development workflow to ensure maintainability and code quality~\cite{Roy2024}.

\section{Security, Maintainability, and Code Quality}

Security is prioritized throughout the implementation, with a focus on secure API development, data validation, and access control~\cite{Kornienko2021}. Maintainability is achieved through modular design, automated testing, and adherence to FAIR and Open/Closed principles~\cite{Hassan2024,Roy2024}. Code quality is continuously monitored using static analysis, code reviews, and CI/CD pipelines.

Best practices include:
\begin{itemize}
    \item \textbf{Authentication and Authorization}: Role-based access control for sensitive operations.
    \item \textbf{Input Validation}: Strict schema validation for all external inputs.
    \item \textbf{Automated Testing}: Comprehensive unit, integration, and end-to-end tests.
    \item \textbf{Documentation}: Inline documentation and API specifications using OpenAPI/Swagger.
\end{itemize}

\section{Implementation Challenges and Lessons Learned}

The implementation of ConversationalBIM presented several technical and organizational challenges:
\begin{itemize}
    \item \textbf{Schema Evolution}: Managing changes in building data schemas across real-time pipelines required robust validation and versioning strategies.
    \item \textbf{Ontology Engineering}: Designing flexible, extensible ontologies for BIM data was complex and required iterative refinement.
    \item \textbf{Integration Complexity}: Orchestrating multiple microservices, data stores, and AI agents necessitated careful interface and dependency management.
    \item \textbf{Performance Optimization}: Achieving low-latency responses for conversational queries involved tuning Kafka, GraphDB, and Qdrant configurations.
    \item \textbf{Security and Robustness}: Ensuring secure data flows and resilience against malformed inputs or adversarial documents was an ongoing effort.
\end{itemize}

Lessons learned include the value of modular, testable components, early investment in ontology and schema design, and the importance of automated quality assurance. Ongoing research opportunities include benchmarking synchronization efficiency, advancing RAG evaluation for technical domains, and improving document processing security.


\section{Overview of the System Architecture}

The ConversationalBIM system is designed as a modular, scalable microservice architecture that integrates natural language processing, semantic data management, and real-time data synchronization to provide an intuitive conversational interface for building information queries. The system is engineered to bridge the gap between complex structured data (RDF graphs), unstructured documents (PDFs, reports), and user-friendly natural language interaction. Figure~\ref{fig:system-architecture} illustrates the high-level architecture of the ConversationalBIM platform.

% Place architectural diagram here
%\begin{figure}[ht]
%    \centering
%    \includegraphics[width=0.95\textwidth]{figures/system_architecture.pdf}
%    \caption{High-level architecture of the ConversationalBIM system.}
%    \label{fig:system-architecture}
%\end{figure}

The architecture is composed of the following main layers:
\begin{itemize}
    \item \textbf{Kafka Integration Layer}: Responsible for real-time ingestion of building data from the ZIM project via Kafka topics.
    \item \textbf{Data Processing Pipeline}: Handles transformation, storage, and semantic enrichment of building data and associated documents.
    \item \textbf{AI Agent Layer}: Provides tool-augmented LLM-based agents for query analysis, graph data retrieval, and document search.
    \item \textbf{API Layer}: Exposes RESTful endpoints for conversational queries, session management, and document processing.
    \item \textbf{Evaluation and Analytics Layer}: Integrates usability and performance measurement, supporting KLM-based evaluation.
\end{itemize}

\section{Detailed Description of System Components}

\subsection{Kafka Integration Layer}

The Kafka Integration Layer serves as the entry point for real-time building data updates from the ZIM PortfolioBIM platform. It subscribes to configurable Kafka topics (e.g., \texttt{data.usecases.*}), processes incoming messages containing building portfolio data in JSON format, and triggers downstream processing. This layer ensures that the ConversationalBIM system remains synchronized with changes in building portfolios, supporting multi-tenant and multi-usecase scenarios. The use of Kafka enables scalable, decoupled communication between the ZIM ecosystem and the ConversationalBIM microservices.

\subsection{Data Processing Pipeline}

The Data Processing Pipeline is responsible for transforming, enriching, and storing building data and associated documents. It consists of several key components:
\begin{itemize}
    \item \textbf{TTL Converter}: Translates incoming JSON building data into RDF/TTL format, enabling semantic representation and interoperability.
    \item \textbf{GraphDB Client}: Stores RDF triples in a semantic graph database and provides SPARQL query capabilities.
    \item \textbf{Document Processor}: Integrates with Docling to extract, chunk, and preprocess content from PDF and other document formats.
    \item \textbf{Vector Store (Qdrant)}: Generates vector embeddings for document chunks, supporting semantic search and retrieval-augmented generation (RAG).
    \item \textbf{MinIO Storage}: Provides object storage for raw and processed documents, ensuring reliable access and backup.
\end{itemize}
This pipeline enables the seamless integration of structured graph data and unstructured document content, supporting complex multi-modal queries.

\subsection{AI Agent Architecture}

The AI Agent Layer is implemented using a tool-augmented LLM framework (PydanticAI) and orchestrates the intelligent processing of user queries. The main agent components are:
\begin{itemize}
    \item \textbf{Orchestrator Agent}: Analyzes incoming natural language queries, determines intent, and delegates tasks to specialized sub-agents.
    \item \textbf{RDF Query Agent}: Translates natural language into SPARQL, executes queries against the GraphDB, and formats structured results.
    \item \textbf{RAG Agent}: Performs semantic search over document embeddings in Qdrant, retrieves relevant content, and generates context-aware responses.
\end{itemize}
The agent-based design enables extensibility, allowing new capabilities (e.g., spatial reasoning, multi-modal input) to be added as separate agents.

\subsection{API Layer}

The API Layer is implemented using FastAPI, providing asynchronous REST endpoints for external integration. It supports:
\begin{itemize}
    \item Natural language and SPARQL query endpoints
    \item Session management for conversational context
    \item Document upload and processing
    \item Administrative and health check operations
\end{itemize}
This layer abstracts the complexity of the underlying data and agent infrastructure, exposing a user-friendly interface for both end-users and system integrators.

\subsection{Evaluation and Analytics Integration}

The Evaluation and Analytics Layer is designed to support systematic usability and performance assessment. It integrates KLM-based operator tracking, query response time measurement, and session analytics. This enables quantitative comparison of conversational and traditional graph-based interfaces, supporting the research objectives of the thesis.

\section{Architectural Diagrams}

To enhance clarity, the following architectural diagrams should be included in the thesis:
\begin{itemize}
    \item \textbf{System Architecture Diagram}: High-level overview of all major components and their interactions (see Figure~\ref{fig:system-architecture}).
    \item \textbf{Data Flow Diagram}: Step-by-step flow of data from Kafka ingestion to user response, illustrating the integration of structured and unstructured data sources (see Figure~\ref{fig:data-flow}).
    \item \textbf{AI Agent Interaction Diagram}: Sequence of agent calls and data exchanges for a typical user query (see Figure~\ref{fig:agent-interaction}).
\end{itemize}
% Example LaTeX code for including diagrams:
%\begin{figure}[ht]
%    \centering
%    \includegraphics[width=0.95\textwidth]{figures/data_flow.pdf}
%    \caption{Data flow from Kafka ingestion to user response.}
%    \label{fig:data-flow}
%\end{figure}

\section{Rationale for Key Design Decisions}

The architectural decisions for ConversationalBIM are guided by the following principles:
\begin{itemize}
    \item \textbf{Modularity and Separation of Concerns}: Each functional area (data ingestion, processing, querying, evaluation) is implemented as an independent service or component, facilitating maintainability and scalability.
    \item \textbf{Tool-Augmented LLMs}: Leveraging agent-based architectures enables the system to combine the reasoning power of LLMs with deterministic tools for graph and document search, improving accuracy and transparency~\cite{Rane2023IntegratingBIMAI, Rafsanjani2023}.
    \item \textbf{Real-Time Synchronization}: Kafka integration ensures that building data remains up-to-date with changes in the ZIM/PortfolioBIM ecosystem, supporting dynamic usecases and multi-user collaboration.
    \item \textbf{Semantic Interoperability}: Adoption of RDF/TTL and standard vocabularies (e.g., Schema.org, IBPDI) allows seamless integration with existing and future building data sources.
    \item \textbf{Extensibility}: The agent-based design and microservice architecture allow for the addition of new capabilities (e.g., advanced spatial queries, voice interfaces) with minimal disruption.
\end{itemize}

\section{Integration with ZIM/PortfolioBIM and Kafka}

ConversationalBIM is designed for seamless integration with the ZIM project PortfolioBIM, supporting real-world building information management workflows. The system:
\begin{itemize}
    \item Listens to Kafka topics for real-time updates on building portfolios and usecases.
    \item Supports IBPDI-compliant data exchange, enabling compatibility with industry standards.
    \item Provides RESTful APIs for frontend integration and session-based conversational context.
    \item Scales to support multiple portfolios, users, and document types.
\end{itemize}
This integration ensures that the conversational interface reflects the latest state of building data, supporting decision-making and collaboration across organizational boundaries.

\section{Scalability, Extensibility, and Limitations}

\subsection{Scalability}

The microservice architecture, use of asynchronous processing (FastAPI, AIOKafka), and distributed storage (GraphDB, Qdrant, MinIO) enable the system to scale horizontally with increasing data volumes and user concurrency. Kafka decouples data producers and consumers, supporting high-throughput ingestion and processing.

\subsection{Extensibility}

The agent-based design and modular pipeline allow new data sources, document types, and reasoning capabilities to be integrated with minimal changes to existing components. The use of standard interfaces and dependency injection (via PydanticAI) facilitates rapid prototyping and extension.

\subsection{Limitations}

Despite its strengths, the current system has several limitations:
\begin{itemize}
    \item \textbf{LLM Dependency}: Reliance on external LLM providers introduces latency and potential availability issues.
    \item \textbf{SPARQL Generation Accuracy}: The quality of natural language to SPARQL translation may vary with query complexity and domain specificity.
    \item \textbf{Document Processing}: Large or poorly structured documents can impact embedding quality and retrieval performance.
    \item \textbf{Domain Adaptation}: Handling highly specialized terminology or spatial queries requires further development.
\end{itemize}
These limitations are addressed in the evaluation and future work sections of the thesis.
