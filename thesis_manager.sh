#!/bin/bash

# ConversationalBIM Thesis Management Utility
# Companion script for monitoring and managing the thesis writing process
# Author: AI Assistant
# Date: $(date +%Y-%m-%d)

set -e

# Configuration
THESIS_DIR="/home/<USER>/dev/ai/bachelorarbeit"
OPENCODE_CMD="opencode --agent thesis-coordinator run"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to show usage
show_usage() {
    echo -e "${CYAN}ConversationalBIM Thesis Manager${NC}"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  status           Check current thesis status"
    echo "  research TOPIC   Research specific topic"
    echo "  write SECTION    Write specific section/chapter"
    echo "  review CONTENT   Review specific content"
    echo "  compile          Compile thesis document"
    echo "  references       Manage references and citations"
    echo "  logs             Show recent log files"
    echo "  progress         Show detailed progress report"
    echo "  backup           Create backup of current work"
    echo "  clean            Clean temporary files"
    echo "  help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 research \"natural language interfaces\""
    echo "  $0 write \"Chapter 1: Introduction\""
    echo "  $0 review \"Chapter 2\""
    echo "  $0 compile"
    echo "  $0 references \"validate citations\""
}

# Logging function
log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[$timestamp]${NC} $message"
}

log_success() {
    local message="$1"
    echo -e "${GREEN}✓${NC} $message"
}

log_warning() {
    local message="$1"
    echo -e "${YELLOW}⚠${NC} $message"
}

log_error() {
    local message="$1"
    echo -e "${RED}✗${NC} $message"
}

# Function to execute OpenCode command
execute_opencode() {
    local message="$1"
    local description="$2"

    log "Executing: $description"
    cd "$THESIS_DIR"

    # Always use thesis-coordinator as single entry point
    if eval "$OPENCODE_CMD \"$message\""; then
        log_success "Completed: $description"
        return 0
    else
        log_error "Failed: $description"
        return 1
    fi
}

# Command functions
cmd_status() {
    echo -e "${CYAN}=== Thesis Status Check ===${NC}"
    execute_opencode "/status" "Checking thesis status"
}

cmd_research() {
    local topic="$1"
    if [[ -z "$topic" ]]; then
        log_error "Research topic required"
        echo "Usage: $0 research \"topic description\""
        exit 1
    fi

    echo -e "${CYAN}=== Research: $topic ===${NC}"
    execute_opencode "Research the topic: $topic. Use @literature-research and @technical-research to find relevant papers and documentation. Save paper information and download links. Save all findings to files in thesis/.context/research/." "Researching: $topic"
}

cmd_write() {
    local section="$1"
    if [[ -z "$section" ]]; then
        log_error "Section/chapter specification required"
        echo "Usage: $0 write \"Chapter/Section title\""
        exit 1
    fi

    echo -e "${CYAN}=== Writing: $section ===${NC}"
    execute_opencode "Write $section. Use @writer to create comprehensive academic content and save to thesis/writing/thesis.tex. Use existing research materials from thesis/.context/research/ for reference." "Writing: $section"
}

cmd_review() {
    local content="$1"
    if [[ -z "$content" ]]; then
        log_error "Content specification required"
        echo "Usage: $0 review \"content to review\""
        exit 1
    fi

    echo -e "${CYAN}=== Review: $content ===${NC}"
    execute_opencode "Review $content. Use @reviewer to check quality, consistency, and academic standards. Save review notes to thesis/.context/reviews/ with timestamp." "Reviewing: $content"
}

cmd_compile() {
    echo -e "${CYAN}=== Compiling Thesis ===${NC}"
    execute_opencode "Compile the thesis document. Use @latex-formatter to handle LaTeX compilation, resolve any errors, and generate final PDF." "Compiling thesis document"
}

cmd_references() {
    local task="$1"
    if [[ -z "$task" ]]; then
        task="validate all citations"
    fi

    echo -e "${CYAN}=== Reference Management: $task ===${NC}"
    execute_opencode "Manage references and citations: $task. Use @reference-manager to handle bibliography, citations, and reference formatting." "Managing references: $task"
}

cmd_logs() {
    echo -e "${CYAN}=== Recent Log Files ===${NC}"
    cd "$THESIS_DIR"
    
    if ls thesis_writing_log_*.log 1> /dev/null 2>&1; then
        echo "Available log files:"
        ls -lt thesis_writing_log_*.log | head -10
        echo ""
        echo "To view a log file: tail -f <filename>"
        echo "To view latest log: tail -f \$(ls -t thesis_writing_log_*.log | head -1)"
    else
        log_warning "No log files found"
    fi
}

cmd_progress() {
    echo -e "${CYAN}=== Detailed Progress Report ===${NC}"
    
    # Check thesis structure
    log "Checking thesis file structure..."
    
    if [[ -f "$THESIS_DIR/thesis/writing/thesis.tex" ]]; then
        log_success "Main thesis file exists"
        
        # Count lines in thesis
        local line_count=$(wc -l < "$THESIS_DIR/thesis/writing/thesis.tex")
        echo "  - Thesis file size: $line_count lines"
        
        # Check for chapters
        echo "  - Chapter analysis:"
        for i in {1..7}; do
            if grep -q "chapter.*$i\|Chapter.*$i" "$THESIS_DIR/thesis/writing/thesis.tex" 2>/dev/null; then
                log_success "    Chapter $i: Present"
            else
                log_warning "    Chapter $i: Missing or incomplete"
            fi
        done
    else
        log_error "Main thesis file not found"
    fi
    
    # Check context materials
    echo ""
    log "Checking research materials..."
    
    local context_dir="$THESIS_DIR/thesis/.context"
    if [[ -d "$context_dir" ]]; then
        log_success "Context directory exists"
        
        # Count research files
        if [[ -d "$context_dir/research" ]]; then
            local research_count=$(find "$context_dir/research" -name "*.md" 2>/dev/null | wc -l)
            echo "  - Research reports: $research_count files"
        fi
        
        # Count papers
        if [[ -d "$context_dir/papers" ]]; then
            local papers_count=$(find "$context_dir/papers" -name "*.pdf" 2>/dev/null | wc -l)
            echo "  - Downloaded papers: $papers_count files"
        fi
        
        # Check references
        if [[ -d "$context_dir/references" ]]; then
            local ref_count=$(find "$context_dir/references" -name "*.bib" 2>/dev/null | wc -l)
            echo "  - Bibliography files: $ref_count files"
        fi
    else
        log_warning "Context directory not found"
    fi
    
    # Run status command for AI assessment
    echo ""
    cmd_status
}

cmd_backup() {
    echo -e "${CYAN}=== Creating Backup ===${NC}"
    
    local backup_dir="$THESIS_DIR/backups"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="thesis_backup_$timestamp"
    
    mkdir -p "$backup_dir"
    
    log "Creating backup: $backup_name"
    
    # Create tar archive of thesis directory
    cd "$THESIS_DIR"
    if tar -czf "$backup_dir/$backup_name.tar.gz" thesis/ .opencode/ *.sh 2>/dev/null; then
        log_success "Backup created: $backup_dir/$backup_name.tar.gz"
        
        # Show backup size
        local size=$(du -h "$backup_dir/$backup_name.tar.gz" | cut -f1)
        echo "  - Backup size: $size"
        
        # List recent backups
        echo "  - Recent backups:"
        ls -lt "$backup_dir"/*.tar.gz 2>/dev/null | head -5 | while read line; do
            echo "    $line"
        done
    else
        log_error "Backup creation failed"
    fi
}

cmd_clean() {
    echo -e "${CYAN}=== Cleaning Temporary Files ===${NC}"
    
    cd "$THESIS_DIR"
    
    # Clean LaTeX auxiliary files
    if [[ -d "thesis/writing" ]]; then
        cd thesis/writing
        log "Cleaning LaTeX auxiliary files..."
        rm -f *.aux *.log *.bbl *.blg *.toc *.lof *.lot *.out *.fdb_latexmk *.fls *.synctex.gz
        log_success "LaTeX auxiliary files cleaned"
        cd "$THESIS_DIR"
    fi
    
    # Clean old log files (keep last 5)
    log "Cleaning old log files..."
    if ls thesis_writing_log_*.log 1> /dev/null 2>&1; then
        local log_count=$(ls thesis_writing_log_*.log | wc -l)
        if [[ $log_count -gt 5 ]]; then
            ls -t thesis_writing_log_*.log | tail -n +6 | xargs rm -f
            log_success "Old log files cleaned (kept 5 most recent)"
        else
            log "No old log files to clean"
        fi
    fi
    
    log_success "Cleanup completed"
}

# Main execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 0
    fi
    
    command="$1"
    shift
    
    case "$command" in
        status)
            cmd_status "$@"
            ;;
        research)
            cmd_research "$@"
            ;;
        write)
            cmd_write "$@"
            ;;
        review)
            cmd_review "$@"
            ;;
        compile)
            cmd_compile "$@"
            ;;
        references)
            cmd_references "$@"
            ;;
        logs)
            cmd_logs "$@"
            ;;
        progress)
            cmd_progress "$@"
            ;;
        backup)
            cmd_backup "$@"
            ;;
        clean)
            cmd_clean "$@"
            ;;
        help)
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
fi
