---
description: Compile and format the thesis document using LaTeX with proper formatting and error checking
agent: latex-formatter
model: github-copilot/gpt-4.1
---

# Compile Thesis: $ARGUMENTS

Compile the ConversationalBIM thesis document with professional formatting and quality checks.

## Critical: Thesis Folder Structure

**IMPORTANT - Understanding thesis folder structure:**

- `thesis/.context/` - Contains research materials, papers, notes, references, and all supporting documentation. This is for **CONTEXT and REFERENCE only**.
- `thesis/writing/` - Contains the actual thesis document (`thesis.tex`) that is being written. This is where the **main LaTeX thesis content lives**.

**When compiling:**

- Compile the main thesis document located at `thesis/writing/thesis.tex`
- The working directory for compilation should be `thesis/writing/`
- Reference materials in `thesis/.context/` are not compiled - they are supporting documentation only
- Always target the actual thesis document, not the context materials

## Compilation Process

1. **Environment Check**: Verify all required LaTeX packages and dependencies
2. **File Validation**: Ensure all referenced files exist and are accessible
3. **Full Compilation**: Execute complete compilation process including bibliography
4. **Error Resolution**: Identify and resolve any compilation errors or warnings
5. **Quality Assessment**: Review compiled output for formatting and presentation quality

## Compilation Steps

```bash
# Navigate to thesis writing directory (NOT the context directory)
cd /home/<USER>/dev/ai/bachelorarbeit/thesis/writing

# Full compilation sequence
pdflatex thesis.tex
biber thesis
pdflatex thesis.tex
pdflatex thesis.tex

# Alternative automated compilation
latexmk -pdf -pvc thesis.tex
```

## Quality Checks

1. **Visual Inspection**: Review PDF output for formatting consistency
2. **Cross-Reference Validation**: Verify all internal references work correctly
3. **Bibliography Integration**: Check citation formatting and bibliography generation
4. **Figure Quality**: Ensure figures are high-resolution and properly positioned
5. **Typography Review**: Check fonts, spacing, and overall presentation

## Error Handling

- **Compilation Errors**: Identify and fix LaTeX syntax errors
- **Missing References**: Resolve undefined citations and cross-references
- **Package Conflicts**: Address LaTeX package compatibility issues
- **File Path Issues**: Fix broken file paths for figures and includes
- **Encoding Problems**: Resolve character encoding issues

## Output Validation

- **PDF Generation**: Confirm clean PDF generation without errors
- **File Size**: Verify reasonable file size with high-quality output
- **Accessibility**: Ensure document structure supports accessibility
- **Print Quality**: Optimize for both digital viewing and printing

## Documentation

- Log any compilation issues and their solutions
- Document any formatting decisions or customizations
- Note any deviations from standard formatting requirements
- Maintain compilation history and version tracking

Ensure the final compiled thesis meets all academic formatting requirements and presents the ConversationalBIM research professionally and clearly.
